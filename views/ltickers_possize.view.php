<?php
#region region DOCS
/** @var TickerPosSize $mod_ticker_possize */
/** @var float $breakout_price */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Calculadora pos. size</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
        <h4>Calculadora position size</h4>

        <hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FORM ?>
        <form action="ltickers_possize" method="POST">
	        <!-- BEGIN row -->
	        <div class="row mt-3">
		        <div class="col-md-6 col-xs-12">
			        <!-- BEGIN row -->
			        <div class="row mt-3">
				        <!-- BEGIN text -->
				        <div class="col-md-12 col-xs-12">
					        <div class="input-group">
                                <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                                    Buying power:
                                </span>
						        <input type="text" name="buying_power" id="buying_power" value="<?php echo @recover_var($mod_ticker_possize->buying_power) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
					        </div>
				        </div>
				        <!-- END text -->
			        </div>
			        <!-- END row -->
			        <!-- BEGIN row -->
			        <div class="row mt-3">
				        <!-- BEGIN text -->
				        <div class="col-md-12 col-xs-12">
					        <div class="input-group">
                                <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                                    Account risk:
                                </span>
						        <input type="text" name="account_risk" id="account_risk" value="<?php echo @recover_var($mod_ticker_possize->account_risk) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
					        </div>
				        </div>
				        <!-- END text -->
			        </div>
			        <!-- END row -->
			        <!-- BEGIN row -->
			        <div class="row mt-3">
				        <!-- BEGIN text -->
				        <div class="col-md-12 col-xs-12">
					        <div class="input-group">
                                <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                                    % after breakout:
                                </span>
						        <input type="text" name="porc_after_breakout" id="porc_after_breakout" value="<?php echo @recover_var($mod_ticker_possize->porc_after_breakout) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
					        </div>
				        </div>
				        <!-- END text -->
			        </div>
			        <!-- END row -->
			        <!-- BEGIN row -->
			        <div class="row mt-3">
				        <!-- BEGIN text -->
				        <div class="col-md-12 col-xs-12">
					        <div class="input-group">
                                <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                                    % stoploss:
                                </span>
						        <input type="text" name="porc_stoploss" id="porc_stoploss" value="<?php echo @recover_var($mod_ticker_possize->porc_stoploss) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
					        </div>
				        </div>
				        <!-- END text -->
			        </div>
			        <!-- END row -->
			        <!-- BEGIN row -->
			        <div class="row mt-3">
				        <!-- BEGIN text -->
				        <div class="col-md-12 col-xs-12">
					        <div class="input-group">
                                <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                                    % profit taking:
                                </span>
						        <input type="text" name="porc_profit_taking" id="porc_profit_taking" value="<?php echo @recover_var($mod_ticker_possize->porc_profit_taking) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
					        </div>
				        </div>
				        <!-- END text -->
			        </div>
			        <!-- END row -->
			        <!-- BEGIN row -->
			        <div class="row mt-3">
				        <!-- BEGIN text -->
				        <div class="col-md-12 col-xs-12">
					        <div class="input-group">
                                <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                                    % first buy:
                                </span>
						        <input type="text" name="porc_first_buy" id="porc_first_buy" value="<?php echo @recover_var($mod_ticker_possize->porc_first_buy) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
					        </div>
				        </div>
				        <!-- END text -->
			        </div>
			        <!-- END row -->
			        <!-- BEGIN row -->
			        <div class="row mt-3">
				        <!-- BEGIN text -->
				        <div class="col-md-12 col-xs-12">
					        <div class="input-group">
                                <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
                                    Breakout spot:
                                </span>
						        <input type="text" name="breakout_spot" id="breakout_spot" value="<?php echo @recover_var($mod_ticker_possize->breakout_spot) ?>" class="form-control form-control-fh bg-primary fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
					        </div>
				        </div>
				        <!-- END text -->
			        </div>
			        <!-- END row -->
			        <!-- BEGIN row -->
			        <div class="row mt-3">
				        <!-- BEGIN text -->
				        <div class="col-md-12 col-xs-12">
					        <div class="input-group">
				                <span class="input-group-text no-border-radious bg-gray fs-12px w-150px">
				                    Stoploss:
				                </span>
						        <input type="text" name="stop" id="stop" value="<?php echo @recover_var($mod_ticker_possize->stoploss) ?>" class="form-control form-control-fh fs-12px no-border-radious bg-danger" onclick="this.focus();this.select('')"/>
					        </div>
				        </div>
				        <!-- END text -->
			        </div>
			        <!-- END row -->
		        </div>
		        <div class="col-md-6 col-xs-12">
			        <?php #region region PANEL calculo ?>
			        <div class="panel panel-inverse mt-3 no-border-radious">
				        <div class="panel-heading no-border-radious">
					        <h4 class="panel-title">
						        Calculo:
					        </h4>
				        </div>
				        <!-- BEGIN PANEL body -->
				        <div class="table-nowrap" style="overflow: auto">
					        <?php #region region TABLE calculo ?>
					        <table class="table table-hover table-sm">
						        <tbody class="fs-12px cursor-pointer">
						        <tr>
							        <td>Buy price:</td>
							        <td class="text-end"><?php echo format_currency_usd_updated($mod_ticker_possize->buy_price); ?></td>
						        </tr>
						        <tr>
							        <td>Trade risk:</td>
							        <td class="text-end"><?php echo format_currency_usd_updated($mod_ticker_possize->trade_risk); ?></td>
						        </tr>
						        <tr>
							        <td>Stoploss:</td>
							        <td class="text-end"><?php echo format_currency_usd_updated($mod_ticker_possize->stoploss); ?></td>
						        </tr>
						        <tr>
							        <td>Profit:</td>
							        <td class="text-end"><?php echo format_currency_usd_updated($mod_ticker_possize->profit); ?></td>
						        </tr>
						        <tr>
							        <td>Position size:</td>
							        <td class="text-end"><?php echo number_format($mod_ticker_possize->position_size, 2); ?></td>
						        </tr>
						        <tr>
							        <td>First buy size:</td>
							        <td class="text-end"><?php echo number_format($mod_ticker_possize->position_size, 2); ?></td>
						        </tr>
						        </tbody>
					        </table>
					        <?php #endregion TABLE calculo ?>
				        </div>
				        <!-- END PANEL body -->
			        </div>
			        <?php #endregion PANEL calculo ?>
		        </div>
	        </div>
	        <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
	            <?php #region region SUBMIT sub_calcular ?>
                <div class="col-md-12 col-xs-12">
                    <button type="submit" id="sub_calcular" name="sub_calcular" class="btn btn-xs btn-primary w-100 no-border-radious">
                        Calcular
                    </button>
                </div>
	            <?php #endregion SUBMIT sub_calcular ?>
            </div>
            <!-- END row -->
        </form>
		<?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $success_text; ?>",
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $error_text; ?>",
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>

<?php #endregion JS ?>

</body>
</html>