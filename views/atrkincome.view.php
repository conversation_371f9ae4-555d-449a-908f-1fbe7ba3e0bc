<?php
#region region DOCS
/** @var Config $actualconfig */
/** @var array $disponible30dias */
/** @var array $disponiblesimulado30dias */
/** @var float $deficit30diasusd */
/** @var float $deficitsimulado30diasusd */
/** @var array $disponible30diasproyectado */
/** @var array $disponiblesimulado30diasproyectado */
/** @var float $deficit30diasusdproyectado */
/** @var float $deficitsimulado30diasusdproyectado */
/** @var float $deficitprimerousd */
/** @var float $deficitprimerousdproyectado */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | TRK Income</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h1 class="page-header">TRK Income</h1>
        <!-- END page-header -->

        <?php #region region FORM ?>
        <form action="atrkincome" method="POST">
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="trkincomeusdgbp" id="trkincomeusdgbp" value="<?php echo @recover_var($actualconfig->trkincomeusdgbp) ?>" placeholder="USD (GBP):" onclick="this.focus();this.select('')"/>
                        <label for="trkincomeusdgbp" class="d-flex align-items-center fs-15px">
                            USD (GBP):
                        </label>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="trkincomeusd" id="trkincomeusd" value="<?php echo @recover_var($actualconfig->trkincomeusd) ?>" placeholder="USD:" onclick="this.focus();this.select('')"/>
                        <label for="trkincomeusd" class="d-flex align-items-center fs-15px">
                            USD:
                        </label>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="trm" id="trm" value="<?php echo @recover_var($actualconfig->trm) ?>" placeholder="TRM:" onclick="this.focus();this.select('')"/>
                        <label for="trm" class="d-flex align-items-center fs-15px">
                            TRM:
                        </label>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="trkincomeusdgbpproyectado" id="trkincomeusdgbpproyectado" value="<?php echo @recover_var($actualconfig->trkincomeproyectadousdgbp) ?>" placeholder="USD (GBP) proyectado:" onclick="this.focus();this.select('')"/>
                        <label for="trkincomeusdgbpproyectado" class="d-flex align-items-center fs-15px">
                            USD (GBP) proyectado:
                        </label>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="trkincomeusdproyectado" id="trkincomeusdproyectado" value="<?php echo @recover_var($actualconfig->trkincomeproyectadousd) ?>" placeholder="USD proyectado:" onclick="this.focus();this.select('')"/>
                        <label for="trkincomeusdproyectado" class="d-flex align-items-center fs-15px">
                            USD proyectado:
                        </label>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="porcfeefrank" id="porcfeefrank" value="<?php echo @recover_var($actualconfig->porcfeefrank) ?>" placeholder="% Fee:" onclick="this.focus();this.select('')"/>
                        <label for="porcfeefrank" class="d-flex align-items-center fs-15px">
                            % Fee:
                        </label>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="feetransfer" id="feetransfer" value="<?php echo @recover_var($actualconfig->feetransfer) ?>" placeholder="Fee transfer:" onclick="this.focus();this.select('')"/>
                        <label for="feetransfer" class="d-flex align-items-center fs-15px">
                            Fee transfer:
                        </label>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <?php #region region SUBMIT sub_save ?>
                <div class="col-md-12 col-xs-12">
                    <button type="submit" id="sub_save" name="sub_save" class="region_SUBMIT_sub_save btn btn-success w-100">
                        Guardar
                    </button>
                </div>
                <?php #endregion sub_save ?>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row">
                <div class="col-md-6 col-xs-12">
                    <?php #region region PANEL resumen ?>
                    <div class="panel panel-inverse mt-3">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                Resumen:
                            </h4>
                        </div>
                        <!-- BEGIN panel-body -->
                        <div class="p-1 table-nowrap" style="overflow: auto">
                            <?php #region region TABLE resumen ?>
                            <table class="table table-hover table-sm">
                                <thead>
                                <tr>
                                    <th>Descripcion</th>
                                    <th>Valor</th>
                                </tr>
                                </thead>
                                <tbody class="fs-12px">
                                <tr>
                                    <td>USD (GBP)</td>
                                    <td class="text-end"><?php echo $actualconfig->trkincomeusdgbp; ?></td>
                                </tr>
                                <tr>
                                    <td>USD</td>
                                    <td class="text-end"><?php echo $actualconfig->trkincomeusd; ?></td>
                                </tr>
                                <tr>
                                    <td>Total:</td>
                                    <td class="text-end"><?php echo $actualconfig->total; ?></td>
                                </tr>
                                <tr>
                                    <td>Fee:</td>
                                    <td class="text-end text-danger"><?php echo $actualconfig->valfeefrank; ?></td>
                                </tr>
                                <tr>
                                    <td>Profit:</td>
                                    <td class="text-end text-success"><?php echo $actualconfig->valprofit; ?></td>
                                </tr>
                                <tr>
                                    <td>Fee 2:</td>
                                    <td class="text-end text-danger"><?php echo $actualconfig->feetransfer; ?></td>
                                </tr>
                                <tr>
                                    <td>Net profit:</td>
                                    <td class="text-end text-success"><?php echo $actualconfig->valnetprofit; ?></td>
                                </tr>
                                <tr>
                                    <td>TRM:</td>
                                    <td class="text-end"><?php echo $actualconfig->trm; ?></td>
                                </tr>
                                <tr>
                                    <td>Profit (COP):</td>
                                    <td class="text-end text-success"><?php echo format_currency($actualconfig->valprofitcop); ?></td>
                                </tr>
                                </tbody>
                            </table>
                            <?php #endregion table resumen ?>
                        </div>
                        <!-- END panel-body -->
                    </div>
                    <?php #endregion panel resumen ?>
                </div>
                <div class="col-md-6 col-xs-12">
                    <?php #region region PANEL resumen proyectado ?>
                    <div class="panel panel-inverse mt-3">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                Resumen proyectado:
                            </h4>
                        </div>
                        <!-- BEGIN panel-body -->
                        <div class="p-1 table-nowrap" style="overflow: auto">
                            <?php #region region TABLE resumen proyectado ?>
                            <table class="table table-hover table-sm">
                                <thead>
                                <tr>
                                    <th>Descripcion</th>
                                    <th>Valor</th>
                                </tr>
                                </thead>
                                <tbody class="fs-12px">
                                <tr>
                                    <td>Subtotal</td>
                                    <td class="text-end"><?php echo $actualconfig->total; ?></td>
                                </tr>
                                <tr>
                                    <td>USD (GBP)</td>
                                    <td class="text-end"><?php echo $actualconfig->trkincomeproyectadousdgbp; ?></td>
                                </tr>
                                <tr>
                                    <td>USD</td>
                                    <td class="text-end"><?php echo $actualconfig->trkincomeproyectadousd; ?></td>
                                </tr>
                                <tr>
                                    <td>Total:</td>
                                    <td class="text-end"><?php echo $actualconfig->totalproyectado; ?></td>
                                </tr>
                                <tr>
                                    <td>Fee:</td>
                                    <td class="text-end text-danger"><?php echo $actualconfig->valfeefrankproyectado; ?></td>
                                </tr>
                                <tr>
                                    <td>Profit:</td>
                                    <td class="text-end text-success"><?php echo $actualconfig->valprofitproyectado; ?></td>
                                </tr>
                                <tr>
                                    <td>Fee 2:</td>
                                    <td class="text-end text-danger"><?php echo $actualconfig->feetransfer; ?></td>
                                </tr>
                                <tr>
                                    <td>Net profit:</td>
                                    <td class="text-end text-success"><?php echo $actualconfig->valnetprofitproyectado; ?></td>
                                </tr>
                                <tr>
                                    <td>TRM:</td>
                                    <td class="text-end"><?php echo $actualconfig->trm; ?></td>
                                </tr>
                                <tr>
                                    <td>Profit (COP):</td>
                                    <td class="text-end text-success"><?php echo format_currency($actualconfig->valprofitcopproyectado); ?></td>
                                </tr>
                                </tbody>
                            </table>
                            <?php #endregion table resumen proyectado ?>
                        </div>
                        <!-- END panel-body -->
                    </div>
                    <?php #endregion panel resumen proyectado ?>
                </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row">
                <div class="col-md-6 col-xs-12">
                    <?php #region region PANEL deficit ?>
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                Deficit:
                            </h4>
                        </div>
                        <!-- BEGIN panel-body -->
                        <div class="panel-body table-nowrap" style="overflow: auto">
                            <?php #region region TABLE deficit ?>
                            <table class="table table-hover table-sm">
                                <thead>
                                <tr>
                                    <th>Descripcion</th>
                                    <th class="text-center">Valor COP</th>
                                    <th class="text-center">Valor USD</th>
                                </tr>
                                </thead>
                                <tbody class="fs-12px">
                                <?php if ($disponible30dias['deficitprimero'] < 0): ?>
                                    <tr>
                                        <td>Deficit mas proximo (<?php echo $disponible30dias['deficitdias']; ?> dias):</td>
                                        <td class="text-end"><?php echo formatCurrencyConSigno(abs($disponible30dias['deficitprimero'])); ?></td>
                                        <td class="text-end">$<?php echo $deficitprimerousd; ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if ($disponible30dias['disponiblesobraxdias'] < 0): ?>
                                    <tr>
                                        <td>Deficit (30 dias):</td>
                                        <td class="text-end"><?php echo formatCurrencyConSigno(abs($disponible30dias['disponiblesobraxdias'])); ?></td>
                                        <td class="text-end">$<?php echo $deficit30diasusd; ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if ($disponiblesimulado30dias['disponiblesobraxdias'] < 0): ?>
                                    <tr>
                                        <td>Deficit simulado (30 dias):</td>
                                        <td class="text-end"><?php echo formatCurrencyConSigno(abs($disponiblesimulado30dias['disponiblesobraxdias'])); ?></td>
                                        <td class="text-end">$<?php echo $deficitsimulado30diasusd; ?></td>
                                    </tr>
                                <?php endif; ?>
                                </tbody>
                            </table>
                            <?php #endregion table deficit ?>
                        </div>
                        <!-- END panel-body -->
                    </div>
                    <?php #endregion panel deficit ?>
                </div>
                <div class="col-md-6 col-xs-12">
                    <?php #region region PANEL deficit proyectado ?>
                    <div class="panel panel-inverse">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                Deficit proyectado:
                            </h4>
                        </div>
                        <!-- BEGIN panel-body -->
                        <div class="panel-body table-nowrap" style="overflow: auto">
                            <?php #region region TABLE deficit proyectado ?>
                            <table class="table table-hover table-sm">
                                <thead>
                                <tr>
                                    <th>Descripcion</th>
                                    <th class="text-center">Valor COP</th>
                                    <th class="text-center">Valor USD</th>
                                </tr>
                                </thead>
                                <tbody class="fs-12px">
                                <?php if ($disponible30diasproyectado['deficitprimero'] < 0): ?>
                                    <tr>
                                        <td>Deficit mas proximo (<?php echo $disponible30diasproyectado['deficitdias']; ?> dias):</td>
                                        <td class="text-end"><?php echo formatCurrencyConSigno(abs($disponible30diasproyectado['deficitprimero'])); ?></td>
                                        <td class="text-end">$<?php echo $deficitprimerousdproyectado; ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if ($disponible30diasproyectado['disponiblesobraxdias'] < 0): ?>
                                    <tr>
                                        <td>Deficit (30 dias):</td>
                                        <td class="text-end"><?php echo formatCurrencyConSigno(abs($disponible30diasproyectado['disponiblesobraxdias'])); ?></td>
                                        <td class="text-end">$<?php echo $deficit30diasusdproyectado; ?></td>
                                    </tr>
                                <?php endif; ?>
                                <?php if ($disponiblesimulado30diasproyectado['disponiblesobraxdias'] < 0): ?>
                                    <tr>
                                        <td>Deficit simulado (30 dias):</td>
                                        <td class="text-end"><?php echo formatCurrencyConSigno(abs($disponiblesimulado30diasproyectado['disponiblesobraxdias'])); ?></td>
                                        <td class="text-end">$<?php echo $deficitsimulado30diasusdproyectado; ?></td>
                                    </tr>
                                <?php endif; ?>
                                </tbody>
                            </table>
                            <?php #endregion table deficit proyectado ?>
                        </div>
                        <!-- END panel-body -->
                    </div>
                    <?php #endregion panel deficit proyectado ?>
                </div>
            </div>
            <!-- END row -->
        </form>
        <?php #endregion form ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script id="region_JS" src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<!-- BEGIN JS pressentersubmit -->
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("trkincomeusdproyectado");

    input.addEventListener("keypress", function(event) {
        if (event.key === "Enter") {
            event.preventDefault();
            document.getElementById("sub_save").click();
        }
    });
</script>
<!-- END JS pressentersubmit -->
<?php #endregion js ?>

</body>
</html>