<?php
#region region DOCS
/** @var string $iddeuda */
/** @var Deuda $moddeuda */
/** @var CategoriaTransaccion[] $categs */
/** @var Transaccion $newtransaccion */
/** @var Budget[] $budgets */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Deudas</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h1 class="page-header">Pagar deuda</h1>
        <!-- END page-header -->

        <?php #region region FORM ?>
        <form action="pdeuda" method="POST">
            <input type="hidden" id="iddeuda" name="iddeuda" value="<?php echo @recover_var($iddeuda) ?>">

            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="nombre" id="nombre" value="<?php echo @recover_var($moddeuda->nombre) ?>" placeholder="Nombre:" readonly/>
                        <label for="nombre" class="d-flex align-items-center fs-15px">
                            Nombre:
                        </label>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="valor" id="valor" value="<?php echo @recover_var($moddeuda->valor) ?>" placeholder="Valor:" readonly/>
                        <label for="valor" class="d-flex align-items-center fs-15px">
                            Valor:
                        </label>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN date -->
                <div class="col-md-12 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px datepicker" id="fechaapagar" name="fechaapagar" placeholder="Nueva fecha a pagar:" value="<?php echo @recover_var($moddeuda->fechaapagar) ?>" autocomplete="off"/>
                        <label for="fechaapagar" class="d-flex align-items-center fs-15px">
                            Nueva fecha a pagar:
                        </label>
                    </div>
                </div>
                <!-- END date -->
            </div>
            <!-- END row -->

            <h4 class="mt-3">Transaccion:</h4>

            <hr>

            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN select -->
                <div class="col-md-12 col-xs-12">
                    <div class="form-floating">
                        <select id="idbudget" name="idbudget" class="form-select">
                            <option value="">--</option>

                            <?php foreach ($budgets as $budget): ?>
                                <option <?php @recover_var_list(ordena($newtransaccion->budget->id_budget), ordena($budget->id_budget)) ?> value="<?php echo limpiar_datos($budget->id_budget); ?>">
                                    <?php echo limpiar_datos($budget->canal); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <label for="idbudget" class="d-flex align-items-center fs-15px">
                            Canal:
                        </label>
                    </div>
                </div>
                <!-- END select -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="valor" id="valor" value="<?php echo @recover_var($newtransaccion->valor) ?>" placeholder="Valor:" onclick="this.focus();this.select('');"/>
                        <label for="valor" class="d-flex align-items-center fs-15px">
                            Valor:
                        </label>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN date -->
                <div class="col-md-6 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px datepicker" id="fecha" name="fecha" placeholder="Fecha:" value="<?php echo @recover_var($newtransaccion->fecha) ?>" autocomplete="off"/>
                        <label for="fecha" class="d-flex align-items-center fs-15px">
                            Fecha:
                        </label>
                    </div>
                </div>
                <!-- END date -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN select2 multiple -->
                <div class="col-md-12 col-xs-12">
                    <label for="categorias" class="align-items-center fs-15px">
                        Categorias:
                    </label>
                    <select id="categorias" name="categorias[]" class="multiple-select2 form-control" multiple>
                        <option value="">--</option>

                        <?php foreach ($categs as $categ): ?>
                            <option value="<?php echo $categ->id; ?>">
                                <?php echo $categ->nombre; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <!-- END select2 multiple -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-12 col-xs-12">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px text-uppercase" name="nota" id="nota" value="<?php echo @recover_var($newtransaccion->nota) ?>" placeholder="Nota:"/>
                        <label for="nota" class="d-flex align-items-center fs-15px">
                            Nota:
                        </label>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN link -->
                <div class="col-md-4 col-xs-12">
                    <a href="ldeudas" class="btn btn-default w-100">
                        Regresar
                    </a>
                </div>
                <!-- END link -->
                <?php #region region SUBMIT sub_add ?>
                <div class="col-md-8 col-xs-12">
                    <button type="submit" id="sub_add" name="sub_add" class="btn btn-success w-100">
                        Agregar
                    </button>
                </div>
                <?php #endregion sub_add ?>
            </div>
            <!-- END row -->
        </form>
        <?php #endregion form ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #region region datejs ?>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/js/datepickerini.js"></script>
<?php #endregion datejs ?>
<?php #region region JS select2 multiple ?>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/select2/dist/js/select2.min.js"></script>

<script type="text/javascript">
    $(".multiple-select2").select2({closeOnSelect: false});
</script>
<?php #endregion JS select2 ?>
<?php #endregion js ?>

</body>
</html>