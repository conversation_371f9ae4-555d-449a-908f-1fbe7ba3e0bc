<?php
#region region DOCS
/** @var Wordkey $mod_wordkey */
/** @var string $id_wordkey */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Wordkeys</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<!-- BEGIN page-header -->
		<h4>Modificar Wordkey</h4>
		
		<hr>
		<!-- END page-header -->
		
		<?php #region region FORM ?>
		<form action="ewordkey" method="POST">
			<input type="hidden" id="id_wordkey" name="id_wordkey" value="<?php echo @recover_var($id_wordkey) ?>">
			
			<!-- BEGIN row -->
			<div class="row">
				<!-- BEGIN text -->
				<div class="col-md-6 col-xs-12">
					<div class="mb-3">
						<label class="form-label">Titulo:</label>
						<input type="text" name="titulo" id="titulo" value="<?php echo @recover_var($mod_wordkey->title) ?>" class="form-control" onclick="this.focus();this.select('')"/>
					</div>
				</div>
				<!-- END text -->
				<!-- BEGIN text -->
				<div class="col-md-6 col-xs-12">
					<div class="mb-3">
						<label class="form-label">Encabezado:</label>
						<input type="text" name="encabezado" id="encabezado" value="<?php echo @recover_var($mod_wordkey->header) ?>" class="form-control" onclick="this.focus();this.select('')"/>
					</div>
				</div>
				<!-- END text -->
			</div>
			<!-- END row -->
			<!-- BEGIN row -->
			<div class="row">
				<!-- BEGIN text -->
				<div class="col-md-6 col-xs-12">
					<div class="mb-3">
						<label class="form-label">Detalle:</label>
						<input type="text" name="detalle" id="detalle" value="<?php echo @recover_var($mod_wordkey->detail) ?>" class="form-control" onclick="this.focus();this.select('')"/>
					</div>
				</div>
				<!-- END text -->
				<!-- BEGIN text -->
				<div class="col-md-6 col-xs-12">
					<div class="mb-3">
						<label class="form-label">Link:</label>
						<input type="text" name="link" id="link" value="<?php echo @recover_var($mod_wordkey->link1) ?>" class="form-control" onclick="this.focus();this.select('')"/>
					</div>
				</div>
				<!-- END text -->
			</div>
			<!-- END row -->
			<!-- BEGIN row -->
			<div class="row">
				<!-- BEGIN textarea -->
				<div class="col-xs-12">
					<label class="form-label">Nota:</label>
					<textarea type="text" id="nota" name="nota" class="form-control" rows="5"><?php echo @recover_var($mod_wordkey->note) ?></textarea>
				</div>
				<!-- END textarea -->
			</div>
			<!-- END row -->
			<!-- BEGIN row -->
			<div class="row mt-3">
				<?php #region region SUBMIT sub_guardar ?>
				<div class="col-md-12 col-xs-12">
					<button type="submit" id="sub_guardar" name="sub_guardar" class="btn btn-md btn-success w-100">
						Guardar
					</button>
				</div>
				<?php #endregion SUBMIT sub_guardar ?>
			</div>
			<!-- END row -->
			<!-- BEGIN row -->
			<div class="row mt-3">
				<?php #region region LINK regresar ?>
				<div class="col-md-12 col-xs-12">
					<a href="awordkeys" class="btn btn-md btn-default w-100">
						Regresar
					</a>
				</div>
				<?php #endregion LINK regresar ?>
			</div>
			<!-- END row -->
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<?php #endregion JS ?>

</body>
</html>