<?php
#region region DOCS
/** @var Fund[] $funds */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Funds</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region region PAGE HEADER ?>
        <h4>Funds</h4>

        <hr>
        <?php #endregion PAGE HEADER ?>

        <?php #region FORM ?>
        <form action="lfunds" method="POST">
            <!-- BEGIN row -->
            <div class="row mt-3">
                <?php #region region LINK agregar ?>
                <div class="col-md-12 col-xs-12">
                    <a href="ifund" class="btn btn-xs btn-primary w-100 no-border-radious">
                        Agregar
                    </a>
                </div>
                <?php #endregion LINK agregar ?>
            </div>
            <!-- END row -->
            <?php #region region PANEL funds ?>
            <div class="panel panel-inverse mt-3 no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">
                        Funds:
                    </h4>
                </div>
                <!-- BEGIN PANEL body -->
                <div class="table-nowrap" style="overflow: auto">
                    <?php #region region TABLE funds ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Fecha</th>
                            <th class="text-center">Banco</th>
                            <th class="text-center">Persona</th>
                            <th class="text-center">Valor fondeo USD</th>
                            <th class="text-center">COP por USD</th>
                            <th class="text-center">Valor fondeo COP</th>
                            <th class="text-center">Costo USD</th>
                            <th class="text-center">TRM</th>
                            <th class="text-center">Costo COP</th>
                            <th class="text-center">Costo IVA</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <?php foreach ($funds as $fund): ?>
                            <tr class="cursor-pointer">
                                <td class="text-center"><?php echo $fund->fecha; ?></td>
                                <td><?php echo $fund->banco; ?></td>
                                <td><?php echo $fund->persona; ?></td>
                                <td class="text-end"><?php echo $fund->valor_fund_usd; ?></td>
                                <td class="text-end"><?php echo $fund->total_cop_porusd; ?></td>
                                <td class="text-end"><?php echo $fund->total_fund_cop; ?></td>
                                <td class="text-end"><?php echo $fund->costo_usd; ?></td>
                                <td class="text-end"><?php echo $fund->trm; ?></td>
                                <td class="text-end"><?php echo $fund->costo_cop; ?></td>
                                <td class="text-end"><?php echo $fund->costo_iva; ?></td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php #region TABLE funds ?>
                </div>
                <!-- END PANEL body -->
            </div>
            <?php #endregion PANEL funds ?>
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $success_text; ?>",
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $error_text; ?>",
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>

<?php #endregion JS ?>

</body>
</html>