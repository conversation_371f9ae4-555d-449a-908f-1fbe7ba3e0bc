<?php
#region region DOCS
/** @var Ticker $mod_ticker */
/** @var TickerGrupo[] $tickers_grupos */
/** @var TickerSubGrupo[] $tickers_subgrupos */
/** @var string $id_ticker */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Tickers</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<link href="<?php echo RUTA ?>resources/assets/plugins/select2/dist/css/select2.min.css" rel="stylesheet"/>
	<link href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
        <h4>Editar ticker <small>Ultima actualización: <?php echo $mod_ticker->fecha_actualizado . ' (' . $mod_ticker->fecha_actualizado_dias . ' dias)'; ?></small></h4>

        <hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FORM ?>
        <form action="eticker" method="POST">
	        <input type="hidden" id="id_ticker" name="id_ticker" value="<?php echo @recover_var($id_ticker) ?>">
	        
	        <?php #region region info general ?>
	        <!-- BEGIN row -->
	        <div class="row mt-3">
		        <!-- BEGIN text -->
		        <div class="col-md-4 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            Nombre:
                        </span>
				        <input type="text" name="nombre" id="nombre" value="<?php echo @recover_var($mod_ticker->nombre) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" readonly autofocus/>
			        </div>
		        </div>
		        <!-- END text -->
		        <!-- BEGIN text -->
		        <div class="col-md-4 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            Grupo:
                        </span>
				        <input type="text" name="grupo" id="grupo" value="<?php echo @recover_var($mod_ticker->ticker_grupo->nombre) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('')"/>
			        </div>
		        </div>
		        <!-- END text -->
		        <!-- BEGIN text -->
		        <div class="col-md-4 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            Subgrupo:
                        </span>
				        <input type="text" name="subgrupo" id="subgrupo" value="<?php echo @recover_var($mod_ticker->ticker_subgrupo->nombre) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase" onclick="this.focus();this.select('')"/>
			        </div>
		        </div>
		        <!-- END text -->
	        </div>
	        <!-- END row -->
	        <!-- BEGIN row -->
	        <div class="row mt-3">
		        <!-- BEGIN date -->
		        <div class="col-md-4 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            Prox. EPS:
                        </span>
				        <input type="text" id="next_eps" name="next_eps" value="<?php echo @recover_var($mod_ticker->next_eps) ?>" class="form-control form-control-fh fs-12px datepicker no-border-radious" autocomplete="off"/>
			        </div>
		        </div>
		        <!-- END date -->
	        </div>
	        <!-- END row -->
	        <?php #endregion info general ?>
	        <!-- BEGIN row -->
	        <div class="row mt-1">
		        <?php #region region eps ?>
		        <div class="col-md-6 col-xs-12">
			        <!-- BEGIN panel -->
			        <div class="panel panel-inverse mt-3 no-border-radious">
				        <div class="panel-heading no-border-radious">
					        EPS
				        </div>
				        <!-- BEGIN panel-body -->
				        <div class="panel-body bg-gray-800 text-white no-border-radious" style="height:165px">
					        <!-- BEGIN row -->
					        <div class="row">
						        <!-- BEGIN switch -->
						        <div class="col-md-4 col-xs-12">
							        <div class="form-check form-switch mt-2 mb-2">
								        <input type="checkbox" id="eps_positivo_q1" name="eps_positivo_q1" <?php echo @recuperar_estado_checked($mod_ticker->eps_positivo_q1); ?> class="form-check-input no-border-radious">
								        <label class="form-check-label fs-14px" for="eps_positive_q1">
									        Q1 positivo
								        </label>
							        </div>
						        </div>
						        <!-- END switch -->
						        <!-- BEGIN switch -->
						        <div class="col-md-4 col-xs-12">
							        <div class="form-check form-switch mt-2 mb-2">
								        <input type="checkbox" id="eps_positivo_q2" name="eps_positivo_q2" <?php echo @recuperar_estado_checked($mod_ticker->eps_positivo_q2); ?> class="form-check-input no-border-radious">
								        <label class="form-check-label fs-14px" for="eps_positivo_q2">
									        Q2 positivo
								        </label>
							        </div>
						        </div>
						        <!-- END switch -->
						        <!-- BEGIN switch -->
						        <div class="col-md-4 col-xs-12">
							        <div class="form-check form-switch mt-2 mb-2">
								        <input type="checkbox" id="eps_positivo_q3" name="eps_positivo_q3" <?php echo @recuperar_estado_checked($mod_ticker->eps_positivo_q3); ?> class="form-check-input no-border-radious">
								        <label class="form-check-label fs-14px" for="eps_positivo_q3">
									        Q3 positivo
								        </label>
							        </div>
						        </div>
						        <!-- END switch -->
					        </div>
					        <!-- END row -->
					        <!-- BEGIN row -->
					        <div class="row mt-3">
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % cambio Q1:
                                        </span>
								        <input type="text" name="eps_porc_change_q1" id="eps_porc_change_q1" value="<?php echo @recover_var($mod_ticker->eps_porc_change_q1) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')" />
							        </div>
						        </div>
						        <!-- END text -->
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % cambio Q2:
                                        </span>
								        <input type="text" name="eps_porc_change_q2" id="eps_porc_change_q2" value="<?php echo @recover_var($mod_ticker->eps_porc_change_q2) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % cambio Q3:
                                        </span>
								        <input type="text" name="eps_porc_change_q3" id="eps_porc_change_q3" value="<?php echo @recover_var($mod_ticker->eps_porc_change_q3) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
					        </div>
					        <!-- END row -->
					        <!-- BEGIN row -->
					        <div class="row mt-3">
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % sorpresa Q1:
                                        </span>
								        <input type="text" name="eps_porc_surprise_q1" id="eps_porc_surprise_q1" value="<?php echo @recover_var($mod_ticker->eps_porc_surprise_q1) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % sorpresa Q2:
                                        </span>
								        <input type="text" name="eps_porc_surprise_q2" id="eps_porc_surprise_q2" value="<?php echo @recover_var($mod_ticker->eps_porc_surprise_q2) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % sorpresa Q3:
                                        </span>
								        <input type="text" name="eps_porc_surprise_q3" id="eps_porc_surprise_q3" value="<?php echo @recover_var($mod_ticker->eps_porc_surprise_q3) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
					        </div>
					        <!-- END row -->
				        </div>
				        <!-- END panel-body -->
			        </div>
			        <!-- END panel -->
		        </div>
		        <?php #endregion eps ?>
		        <?php #region region sales ?>
		        <div class="col-md-6 col-xs-12">
			        <!-- BEGIN panel -->
			        <div class="panel panel-inverse mt-3 no-border-radious">
				        <div class="panel-heading no-border-radious">
					        Sales
				        </div>
				        <!-- BEGIN panel-body -->
				        <div class="panel-body bg-gray-800 text-white no-border-radious" style="height:165px">
					        <!-- BEGIN row -->
					        <div class="row">
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % change Q1:
                                        </span>
								        <input type="text" name="sales_porc_change_q1" id="sales_porc_change_q1" value="<?php echo @recover_var($mod_ticker->sales_porc_change_q1) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % change Q2:
                                        </span>
								        <input type="text" name="sales_porc_change_q2" id="sales_porc_change_q2" value="<?php echo @recover_var($mod_ticker->sales_porc_change_q2) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % change Q3:
                                        </span>
								        <input type="text" name="sales_porc_change_q3" id="sales_porc_change_q1" value="<?php echo @recover_var($mod_ticker->sales_porc_change_q3) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
					        </div>
					        <!-- END row -->
					        <!-- BEGIN row -->
					        <div class="row mt-3">
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % surprise Q1:
                                        </span>
								        <input type="text" name="sales_porc_surprise_q1" id="sales_porc_surprise_q1" value="<?php echo @recover_var($mod_ticker->sales_porc_surprise_q1) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % surprise Q2:
                                        </span>
								        <input type="text" name="sales_porc_surprise_q2" id="sales_porc_surprise_q2" value="<?php echo @recover_var($mod_ticker->sales_porc_surprise_q2) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
						        <!-- BEGIN text -->
						        <div class="col-md-4 col-xs-12">
							        <div class="input-group">
                                        <span class="input-group-text no-border-radious bg-gray fs-12px w-120px">
                                            % surprise Q3:
                                        </span>
								        <input type="text" name="sales_porc_surprise_q3" id="sales_porc_surprise_q1" value="<?php echo @recover_var($mod_ticker->sales_porc_surprise_q3) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
							        </div>
						        </div>
						        <!-- END text -->
					        </div>
					        <!-- END row -->
				        </div>
				        <!-- END panel-body -->
			        </div>
			        <!-- END panel -->
		        </div>
		        <?php #endregion sales ?>
	        </div>
	        <!-- END row -->
	        <?php #region region ta analysis ?>
	        <!-- BEGIN row -->
	        <div class="row mt-3">
		        <!-- BEGIN text -->
		        <div class="col-md-12 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            RSI:
                        </span>
				        <input type="text" name="rsi" id="rsi" value="<?php echo @recover_var($mod_ticker->rsi) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
			        </div>
		        </div>
		        <!-- END text -->
	        </div>
	        <!-- END row -->
	        <!-- BEGIN row -->
	        <div class="row mt-3">
		        <!-- BEGIN switch -->
		        <div class="col-md-2 col-xs-12">
			        <div class="form-check form-switch mt-2 mb-2">
				        <input type="checkbox" id="sma_1mo_up" name="sma_1mo_up" <?php echo @recuperar_estado_checked($mod_ticker->sma_1mo_up); ?> class="form-check-input no-border-radious">
				        <label class="form-check-label fs-14px" for="sma_1mo_up">
					        SMA 1Mo Up
				        </label>
			        </div>
		        </div>
		        <!-- END switch -->
		        <!-- BEGIN switch -->
		        <div class="col-md-2 col-xs-12">
			        <div class="form-check form-switch mt-2 mb-2">
				        <input type="checkbox" id="sma_4mo_up" name="sma_4mo_up" <?php echo @recuperar_estado_checked($mod_ticker->sma_4mo_up); ?> class="form-check-input no-border-radious">
				        <label class="form-check-label fs-14px" for="sma_4mo_up">
					        SMA 4Mo Up
				        </label>
			        </div>
		        </div>
		        <!-- END switch -->
	        </div>
	        <!-- END row -->
	        <!-- BEGIN row -->
	        <div class="row mt-3">
		        <!-- BEGIN text -->
		        <div class="col-md-3 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            Base:
                        </span>
				        <input type="text" name="base" id="base" value="<?php echo @recover_var($mod_ticker->base) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
			        </div>
		        </div>
		        <!-- END text -->
		        <!-- BEGIN text -->
		        <div class="col-md-3 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            T1:
                        </span>
				        <input type="text" name="t1" id="t1" value="<?php echo @recover_var($mod_ticker->t1) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
			        </div>
		        </div>
		        <!-- END text -->
		        <!-- BEGIN text -->
		        <div class="col-md-3 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            T2:
                        </span>
				        <input type="text" name="t2" id="t2" value="<?php echo @recover_var($mod_ticker->t2) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
			        </div>
		        </div>
		        <!-- END text -->
		        <!-- BEGIN text -->
		        <div class="col-md-3 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            T3:
                        </span>
				        <input type="text" name="t3" id="t3" value="<?php echo @recover_var($mod_ticker->t3) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
			        </div>
		        </div>
		        <!-- END text -->
	        </div>
	        <!-- END row -->
	        <!-- BEGIN row -->
	        <div class="row mt-3">
		        <!-- BEGIN text -->
		        <div class="col-md-3 col-xs-12">
			        <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-100px">
                            Caruso bars:
                        </span>
				        <input type="text" name="caruso_bars" id="caruso_bars" value="<?php echo @recover_var($mod_ticker->caruso_bars) ?>" class="form-control form-control-fh fs-12px no-border-radious " onclick="this.focus();this.select('')"/>
			        </div>
		        </div>
		        <!-- END text -->
	        </div>
	        <!-- END row -->
	        <?php #endregion ta analysis ?>
	        <!-- BEGIN row -->
	        <div class="row mt-3">
		        <?php #region region LINK regresar ?>
		        <div class="col-md-4 col-xs-12">
			        <a href="ltickers" class="btn btn-xs btn-default w-100 no-border-radious">
				        Regresar
			        </a>
		        </div>
		        <?php #endregion LINK regresar ?>
		        <?php #region region SUBMIT sub_modificar ?>
		        <div class="col-md-8 col-xs-12">
			        <button type="submit" id="sub_modificar" name="sub_modificar" class="btn btn-xs btn-success w-100 no-border-radious">
				        Modificar
			        </button>
		        </div>
		        <?php #endregion SUBMIT sub_modificar ?>
	        </div>
	        <!-- END row -->
        </form>
		<?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $success_text; ?>",
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $error_text; ?>",
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>
<?php #region region js select2 ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/select2/dist/js/select2.min.js"></script>

<script type="text/javascript">
    $(".default-select2").select2();
</script>
<?php #endregion js select2 ?>
<?php #region region JS date ?>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/js/datepickerini.js"></script>
<?php #endregion JS date ?>
<?php #region region JS autocomplete grupo ?>
<script>
    $("#grupo").autocomplete({
        source: [
			<?php foreach ($tickers_grupos as $ticker_grupo): ?>
            "<?php echo $ticker_grupo->nombre; ?>",
			<?php endforeach; ?>
        ]
    });
</script>
<?php #endregion JS autocomplete grupo ?>
<?php #region region JS autocomplete subgrupo ?>
<script>
    $("#subgrupo").autocomplete({
        source: [
			<?php foreach ($tickers_subgrupos as $ticker_subgrupo): ?>
            "<?php echo $ticker_subgrupo->nombre; ?>",
			<?php endforeach; ?>
        ]
    });
</script>
<?php #endregion JS autocomplete subgrupo ?>

<?php #endregion JS ?>

</body>
</html>