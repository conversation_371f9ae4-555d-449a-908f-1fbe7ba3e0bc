<?php
#region region DOCS
/** @var Partido[] $partidos_weeks */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Partidos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <!-- BEGIN head -->
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <!-- BEGIN daterange css-->
    <link id="region_CSS_daterange" href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet"/>
    <!-- END daterange css-->
    <!-- END head-->
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h4>Reporte - Profit por semana</h4>

        <hr>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="cpartidoreporte_profitsemanal" method="POST">
            <!-- BEGIN ROW -->
            <div class="row mt-3">
                <!-- BEGIN daterange -->
                <div class="col-md-12 col-xs-12">
                    <div id="fecha">
                        <label for="fecharange" class="d-flex align-items-center fs-12px">
                            Fecha:
                        </label>
                        <input type="text" class="form-control form-control-fh fs-12px no-border-radious" id="fecharange" name="fecharange" value="<?php echo @recover_var($fecharange) ?>"/>
                    </div>
                </div>
                <!-- END daterange -->
            </div>
            <!-- END ROW -->
            <!-- ROW -->
            <div class="row mt-3">
                <!-- sub_search -->
                <div class="col-md-8 col-xs-12">
                    <button type="submit" id="sub_search" name="sub_search" class="region_SUBMIT_sub_search btn btn-xs btn-primary w-100 no-border-radious">
                        Buscar
                    </button>
                </div>
                <!-- END sub_search -->
                <!-- BEGIN link -->
                <div class="col-md-4 col-xs-12">
                    <a href="lpartidosreportes" class="region_LINK_regresar btn btn-default w-100 no-border-radious">
                        Regresar
                    </a>
                </div>
                <!-- END link -->
            </div>
            <!-- END ROW -->
            <!-- PANEL -->
            <div class="region_PANEL_profitsemanal panel panel-inverse mt-3 no-border-radious">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Profit por semana:
                    </h4>
                </div>
                <!-- BEGIN PANEL body -->
                <div class="table-nowrap" style="overflow: auto">
                    <!-- TABLE -->
                    <table class="region_TABLE_profitsemanal table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Semana</th>
                            <th class="text-center"># apuestas</th>
                            <th class="text-center">Win ratio</th>
                            <th class="text-center">Apostado</th>
                            <th class="text-center">Profit $</th>
                            <th class="text-center">Profit %</th>
                        </tr>
                        </thead>
                        <tbody class="region_ARRAY_profitsemanal fs-14px">
                            <tr class="<?php echo ($reporte_lifetime['porc_profit'] >= 0) ? COLOR_SUCCESS : COLOR_DANGER; ?>">
                                <td>Lifetime:</td>
                                <td class="text-center"><?php echo $reporte_lifetime['num_apuestas']; ?></td>
                                <td class="text-end"><?php echo $reporte_lifetime['winratio']; ?>%</td>
                                <td class="text-end"><?php echo formatCurrencyConSigno($reporte_lifetime['val_apostado']); ?></td>
                                <td class="text-end"><?php echo formatCurrencyConSigno($reporte_lifetime['val_profit']); ?></td>
                                <td class="text-end"><?php echo $reporte_lifetime['porc_profit']; ?>%</td>
                            </tr>
                        <?php foreach ($partidos_weeks as $partido_week): ?>
                            <tr class="<?php echo ($partido_week->reporte_profitsemanal['porc_profit'] >= 0) ? COLOR_SUCCESS : COLOR_DANGER; ?>">
                                <td><?php echo $partido_week->num_week; ?>:</td>
                                <td class="text-center"><?php echo $partido_week->reporte_profitsemanal['num_apuestas']; ?></td>
                                <td class="text-end"><?php echo $partido_week->reporte_profitsemanal['winratio']; ?>%</td>
                                <td class="text-end"><?php echo formatCurrencyConSigno($partido_week->reporte_profitsemanal['val_apostado']); ?></td>
                                <td class="text-end"><?php echo formatCurrencyConSigno($partido_week->reporte_profitsemanal['val_profit']); ?></td>
                                <td class="text-end"><?php echo $partido_week->reporte_profitsemanal['porc_profit']; ?>%</td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <!-- END TABLE -->
                </div>
                <!-- END PANEL body -->
            </div>
            <!-- END PANEL -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script id="region_JS" src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<!-- BEGIN JS daterange -->
<script id="region_JS_daterange" src="<?php echo RUTA ?>resources/assets/plugins/moment/min/moment.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-daterangepicker/daterangepicker.js"></script>
<script type="text/javascript">
    $("#fecha").daterangepicker({
        autoApply: true,
        format: "YYYY-MM-DD",
        separator: " to ",
        startDate: moment().subtract("days", 1),
        endDate: moment(),
    }, function (start, end) {
        $("#fecha input").val(start.format("YYYY-MM-DD") + " - " + end.format("YYYY-MM-DD"));
    });
</script>
<!-- END JS daterange -->
<?php #endregion JS ?>

</body>
</html>