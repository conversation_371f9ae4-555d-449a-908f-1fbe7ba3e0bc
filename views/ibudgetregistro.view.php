<?php #region docs
/** @var BudgetRegistro $newbudgetregistro */
/** @var array $budgets */
/** @var Budget $budget */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Budget</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <!-- #head -->
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet" />
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/sidebar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <?php #region form ?>
        <form action="ibudgetregistro" method="POST">
            <!-- BEGIN page-header -->
            <h1 class="page-header">
                Add budget record
                <button type="submit" id="sub_ajuste" name="sub_ajuste" class="btn btn-primary btn-md">
                    Ajuste
                </button>
            </h1>
            <!-- END page-header -->
            <!-- BEGIN row -->
            <div class="row">
                <div class="col">
                    <span class="fs-15px lh-lg">
                        Cuenta:<br>
                    </span>
                    <div class="btn-group btn-group-toggle my-n1 w-100" data-toggle="buttons">
                        <?php foreach ($budgets as $budget): ?>
                            <input type="radio" name="id_budget" id="id_budget<?php echo $budget->canal; ?>" class="btn-check" value="<?php echo $budget->id_budget; ?>" <?php @recoverradio('<?php echo ordena($budget->id_budget); ?>', ordena($newbudgetregistro->id_budget)) ?> />
                            <label class="btn btn-primary btn-lg" for="id_budget<?php echo $budget->canal; ?>"><?php echo $budget->canal; ?></label>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <div class="col">
                    <span class="fs-15px lh-lg">
                        Categoria:<br>
                    </span>
                    <div class="btn-group btn-group-toggle my-n1 w-100" data-toggle="buttons">
                        <input type="radio" name="categoria" id="categoriacompra" class="btn-check" value="Compra" <?php @recoverradio('Compra', $newbudgetregistro->categoria) ?> />
                        <label class="btn btn-primary btn-lg" for="categoriacompra">Compra</label>
                        <input type="radio" name="categoria" id="categoriadeuda" class="btn-check" value="Deuda" <?php @recoverradio('Deuda', $newbudgetregistro->categoria) ?> />
                        <label class="btn btn-primary btn-lg" for="categoriadeuda">Deuda</label>
                        <input type="radio" name="categoria" id="categoriadomicilio" class="btn-check" value="Domicilio" <?php @recoverradio('Domicilio', $newbudgetregistro->categoria) ?> />
                        <label class="btn btn-primary btn-lg" for="categoriadomicilio">Domicilio</label>
                        <input type="radio" name="categoria" id="categoriagasto" class="btn-check" value="Gasto" <?php @recoverradio('Gasto', $newbudgetregistro->categoria) ?> />
                        <label class="btn btn-primary btn-lg" for="categoriagasto">Gasto</label>
                        <input type="radio" name="categoria" id="categoriamoto" class="btn-check" value="Moto" <?php @recoverradio('Moto', $newbudgetregistro->categoria) ?> />
                        <label class="btn btn-primary btn-lg" for="categoriamoto">Moto</label>
                        <input type="radio" name="categoria" id="categoriatransporte" class="btn-check" value="Transporte" <?php @recoverradio('Transporte', $newbudgetregistro->categoria) ?> />
                        <label class="btn btn-primary btn-lg" for="categoriatransporte">Transporte</label>
                        <input type="radio" name="categoria" id="categoriaingreso" class="btn-check" value="Ingreso" <?php @recoverradio('Ingreso', $newbudgetregistro->categoria) ?> />
                        <label class="btn btn-success btn-lg" for="categoriaingreso">Ingreso</label>
                        <input type="radio" name="categoria" id="categoriaajuste" class="btn-check" value="Ajuste" <?php @recoverradio('Ajuste', $newbudgetregistro->categoria) ?> />
                        <label class="btn btn-default btn-lg" for="categoriaajuste">Ajuste</label>
                    </div>
                </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-4">
                <div class="col">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="subcategoria" id="subcategoria" value="<?php echo @recover_var($newbudgetregistro->subcategoria) ?>" placeholder="Subcategoria:"/>
                        <label for="subcategoria" class="d-flex align-items-center fs-15px">
                            Subcategoria:
                        </label>
                    </div>
                </div>
                <div class="col">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px" name="valor" id="valor" value="<?php echo @recover_var($newbudgetregistro->valor) ?>" placeholder="Valor:" onclick="this.focus();this.select();"/>
                        <label for="valor" class="d-flex align-items-center fs-15px">
                            Valor:
                        </label>
                    </div>
                </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <div class="col">
                    <div class="form-floating">
                        <input type="text" class="form-control fs-15px datepicker" id="fch" name="fch" placeholder="Fecha:" value="<?php echo @recover_var($newbudgetregistro->fch) ?>"/>
                        <label for="fch" class="d-flex align-items-center fs-15px">
                            Fecha:
                        </label>
                    </div>
                </div>
                <div class="col-2">
                    <button type="submit" id="sub_today" name="sub_today" class="btn btn-primary btn-inline w-100" data-toggle="tooltip" data-placement="top" title="Hoy">
                        <i class="fa fa-calendar fa-lg fa-fw"></i>
                    </button>
                </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <div class="col">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="isajuste" name="isajuste" <?php @recvarcheck($newbudgetregistro->isajuste) ?> />
                        <label class="form-check-label semi-bold" for="isajuste">Ajuste</label>
                    </div>
                </div>
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <div class="col-2">
                    <a class="btn btn-default w-100" href="lbudgetregistros">
                        <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                    </a>
                </div>
                <?php #region sub_add ?>
                <div class="col">
                    <button type="submit" id="sub_add" name="sub_add" class="btn btn-success w-100">
                        Guardar
                    </button>
                </div>
                <?php #endregion sub_add ?>
                <?php #region sub_addexit ?>
                <div class="col">
                    <button type="submit" id="sub_addexit" name="sub_addexit" class="btn btn-success w-100">
                        Guardar y salir
                    </button>
                </div>
                <?php #endregion sub_addexit ?>
            </div>
            <!-- END row -->
        </form>
        <?php #endregion form ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region js ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<script src="<?php echo RUTA ?>resources/js/fab.js"></script>
<script src="<?php echo RUTA ?>resources/js/formatcurrency.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/jquery.maskedinput/src/jquery.maskedinput.js"></script>

<?php require_once __ROOT__ . '/views/datepickerjs.view.php'; ?>

<script type="text/javascript">
    $("#fch").mask("9999-99-99");
</script>

<?php #region js press ?>
<script type="text/javascript">
    pressenterandfocus('categoria', 'subcategoria');
    pressenterandfocus('subcategoria', 'id_budget');
    pressenterandfocus('id_budget', 'fch');
    pressenterandfocus('fch', 'valor');
    pressenterandclick('valor', 'sub_add');
</script>
<?php #endregion js press ?>
<?php #endregion js ?>

</body>
</html>