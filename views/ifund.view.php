<?php
#region region DOCS
/** @var Fund $newfund */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Funds</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN breadcrumb -->
        <ol class="breadcrumb float-xl-end">
            <li class="breadcrumb-item"><a href="dashboard">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="lfunds">Funds list</a></li>
            <li class="breadcrumb-item active">Add funds</li>
        </ol>
        <!-- END breadcrumb -->
        <?php #region region PAGE HEADER ?>
        <h4>Agregar fund</h4>

        <hr>
        <?php #endregion PAGE HEADER ?>

        <?php #region region FORM ?>
        <form action="ifund" method="POST">
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN date -->
                <div class="col-md-4 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            Fecha:
                        </span>
                        <input type="text" id="fecha" name="fecha" value="<?php echo @recover_var($newfund->fecha) ?>" class="form-control form-control-fh fs-12px datepicker no-border-radious" autocomplete="off"/>
                    </div>
                </div>
                <!-- END date -->
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            Banco:
                        </span>
                        <input type="text" name="banco" id="banco" value="<?php echo @recover_var($newfund->banco) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase"/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            Persona:
                        </span>
                        <input type="text" name="persona" id="persona" value="<?php echo @recover_var($newfund->persona) ?>" class="form-control form-control-fh fs-12px no-border-radious text-uppercase"/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            Valor fondeo USD:
                        </span>
                        <input type="text" name="valor_fund_usd" id="valor_fund_usd" value="<?php echo @recover_var($newfund->valor_fund_usd) ?>" class="form-control form-control-fh fs-12px no-border-radious "/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            COP por USD:
                        </span>
                        <input type="text" name="total_cop_porusd" id="total_cop_porusd" value="<?php echo @recover_var($newfund->total_cop_porusd) ?>" class="form-control form-control-fh fs-12px no-border-radious "/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-4 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            Valor fondeo COP:
                        </span>
                        <input type="text" name="total_fund_cop" id="total_fund_cop" value="<?php echo @recover_var($newfund->total_fund_cop) ?>" class="form-control form-control-fh fs-12px no-border-radious "/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            Costo USD:
                        </span>
                        <input type="text" name="costo_usd" id="costo_usd" value="<?php echo @recover_var($newfund->costo_usd) ?>" class="form-control form-control-fh fs-12px no-border-radious "/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            TRM:
                        </span>
                        <input type="text" name="trm" id="trm" value="<?php echo @recover_var($newfund->trm) ?>" class="form-control form-control-fh fs-12px no-border-radious "/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            Costo COP:
                        </span>
                        <input type="text" name="costo_cop" id="costo_cop" value="<?php echo @recover_var($newfund->costo_cop) ?>" class="form-control form-control-fh fs-12px no-border-radious "/>
                    </div>
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-3 col-xs-12">
                    <div class="input-group">
                        <span class="input-group-text no-border-radious bg-gray fs-12px w-140px">
                            Costo IVA:
                        </span>
                        <input type="text" name="costo_iva" id="costo_iva" value="<?php echo @recover_var($newfund->costo_iva) ?>" class="form-control form-control-fh fs-12px no-border-radious "/>
                    </div>
                </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <?php #region region SUBMIT sub_agregar ?>
                <div class="col-md-12 col-xs-12">
                    <button type="submit" id="sub_agregar" name="sub_agregar" class="btn btn-xs btn-success w-100 no-border-radious">
                        Agregar
                    </button>
                </div>
                <?php #endregion SUBMIT sub_agregar ?>
            </div>
            <!-- END row -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $success_text; ?>",
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $error_text; ?>",
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>
<?php #region region datejs ?>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/js/datepickerini.js"></script>
<?php #endregion datejs ?>
<?php #endregion JS ?>

</body>
</html>