/*!
  Theme: Spacemacs
  Author: <PERSON><PERSON> (https://github.com/nasham<PERSON>/spacemacs-theme)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#a3a3a3;background:#1f2022}.hljs ::selection,.hljs::selection{background-color:#444155;color:#a3a3a3}.hljs-comment{color:#585858}.hljs-tag{color:#b8b8b8}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#a3a3a3}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#f2241f}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:orange}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#b1951d}.hljs-strong{font-weight:700;color:#b1951d}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#67b11d}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#2d9574}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#4f97d7}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#a31db1}.hljs-emphasis{color:#a31db1;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b03060}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}