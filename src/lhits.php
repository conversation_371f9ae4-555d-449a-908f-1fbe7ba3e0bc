<?php

declare(strict_types=1);

/** @var PDO $conexion */
global $conexion;

use App\classes\Hit;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

$newhit = new Hit;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		if (isset($_GET['m'])) {
			$success_display = 'show';
			$success_text    = 'El hit ha sido modificado.';
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get

#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
	try {
		// Set timezone to Bogotá, Colombia
		setTimeZoneCol();

		// Debug: Log the original pago value
		$originalPago = $_POST['pago'];
		$cleanedPago  = limpiar_datos($_POST['pago']);
		$floatPago    = (float)$cleanedPago;
		error_log("DEBUG - Original pago: " . var_export($originalPago, true));
		error_log("DEBUG - Cleaned pago: " . var_export($cleanedPago, true));
		error_log("DEBUG - Float pago: " . var_export($floatPago, true));

		// Use setters and cast pago to float
		$newhit->setDescripcion(limpiar_datos($_POST['descripcion']));
		$newhit->setRequester(limpiar_datos($_POST['requester']));
		$newhit->setPago($floatPago);                    // Use the debugged float value
		$newhit->setNota(limpiar_datos($_POST['nota'])); // Set nota
		$newhit->setFecha(create_datetime());            // Set current datetime in Bogotá timezone

		// Use guardar method
		$newhit->guardar($conexion);

		$success_display = 'show';
		$success_text    = 'El hit ha sido ingresado.';

		$newhit = new Hit();

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_add

#region sub_delhit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delhit'])) {
	try {
		$delidhit = limpiar_datos($_POST['mdl_delhit_idhit']);

		Hit::delete($delidhit, $conexion);

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_delhit

#region sub_edithit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_edithit'])) {
	try {
		$_SESSION['idhit'] = limpiar_datos($_POST['selidhit']);

		header('Location: ehit');

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_edithit

#region sub_update_hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_update_hit'])) {
	try {
		// Set timezone to Bogotá, Colombia
		setTimeZoneCol();

		$hitId = limpiar_datos($_POST['edit_hit_id']);
		$hit   = Hit::get($hitId, $conexion);

		if ($hit) {
			// Debug: Log the original edit pago value
			$originalEditPago = $_POST['edit_pago'];
			$cleanedEditPago  = limpiar_datos($_POST['edit_pago']);
			$floatEditPago    = (float)$cleanedEditPago;
			error_log("DEBUG EDIT - Original pago: " . var_export($originalEditPago, true));
			error_log("DEBUG EDIT - Cleaned pago: " . var_export($cleanedEditPago, true));
			error_log("DEBUG EDIT - Float pago: " . var_export($floatEditPago, true));

			// Update hit data using setters and cast pago to float
			$hit->setDescripcion(limpiar_datos($_POST['edit_descripcion']));
			$hit->setRequester(limpiar_datos($_POST['edit_requester']));
			$hit->setPago($floatEditPago);                     // Use the debugged float value
			$hit->setNota(limpiar_datos($_POST['edit_nota'])); // Set nota

			// Use guardar method to update
			$hit->guardar($conexion);

			$success_display = 'show';
			$success_text    = 'El hit ha sido actualizado.';
		} else {
			throw new Exception('Hit no encontrado.');
		}

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_update_hit

#region sub_finish_hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_finish_hit'])) {
	try {
		// Set timezone to Bogotá, Colombia
		setTimeZoneCol();

		$hitId = limpiar_datos($_POST['finish_hit_id']);
		Hit::markAsFinished($hitId, $conexion);

		$success_display = 'show';
		$success_text    = 'El hit ha sido marcado como terminado.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_finish_hit

#region sub_return_hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_return_hit'])) {
	try {
		$hitId = limpiar_datos($_POST['return_hit_id']);
		Hit::markAsReturned($hitId, $conexion);

		$success_display = 'show';
		$success_text    = 'El hit ha sido marcado como retornado.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_return_hit

#region AJAX handlers
// AJAX handler for finish hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_finish_hit'])) {
	header('Content-Type: application/json');
	try {
		setTimeZoneCol();
		$hitId = limpiar_datos($_POST['hit_id']);
		Hit::markAsFinished($hitId, $conexion);
		echo json_encode(['success' => true, 'message' => 'Hit marcado como terminado']);
	} catch (Exception $e) {
		echo json_encode(['success' => false, 'message' => $e->getMessage()]);
	}
	exit;
}

// AJAX handler for return hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_return_hit'])) {
	header('Content-Type: application/json');
	try {
		$hitId = limpiar_datos($_POST['hit_id']);
		Hit::markAsReturned($hitId, $conexion);
		echo json_encode(['success' => true, 'message' => 'Hit marcado como retornado']);
	} catch (Exception $e) {
		echo json_encode(['success' => false, 'message' => $e->getMessage()]);
	}
	exit;
}

// AJAX handler for get hit data
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_get_hit'])) {
	header('Content-Type: application/json');
	try {
		$hitId = limpiar_datos($_POST['hit_id']);
		$hit   = Hit::get($hitId, $conexion);
		if ($hit) {
			echo json_encode([
				                 'success' => true,
				                 'data'    => [
					                 'id'          => $hit->getId(),
					                 'descripcion' => $hit->getDescripcion(),
					                 'requester'   => $hit->getRequester(),
					                 'pago'        => $hit->getPago(),
					                 'nota'        => $hit->getNota()
				                 ]
			                 ]);
		} else {
			echo json_encode(['success' => false, 'message' => 'Hit no encontrado']);
		}
	} catch (Exception $e) {
		echo json_encode(['success' => false, 'message' => $e->getMessage()]);
	}
	exit;
}

// AJAX handler for get current active hit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_get_current_active_hit'])) {
	header('Content-Type: application/json');
	try {
		setTimeZoneCol(); // Set Bogotá timezone
		$hit = Hit::getCurrentActiveHit($conexion);
		if ($hit) {
			echo json_encode([
				                 'success'      => true,
				                 'data'         => [
					                 'id'          => $hit->getId(),
					                 'descripcion' => $hit->getDescripcion(),
					                 'requester'   => $hit->getRequester(),
					                 'pago'        => $hit->getPago(),
					                 'fecha'       => $hit->getFecha(),
					                 'nota'        => $hit->getNota()
				                 ],
				                 'current_time' => date('Y-m-d H:i:s') // Current server time in Bogotá timezone
			                 ]);
		} else {
			echo json_encode(['success' => false, 'message' => 'No hay hit activo']);
		}
	} catch (Exception $e) {
		echo json_encode(['success' => false, 'message' => $e->getMessage()]);
	}
	exit;
}
#endregion AJAX handlers

#region try
try {
	$hits   = Hit::getTodaysList($conexion);
	$trk_id = Config::getTrkId($conexion);
	$todays_earnings = Hit::getTodaysEarnings($conexion);
	$weekly_earnings = Hit::getWeeklyEarnings($conexion);
	$monthly_earnings = Hit::getMonthlyEarnings($conexion);

} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lhits.view.php';

?>
