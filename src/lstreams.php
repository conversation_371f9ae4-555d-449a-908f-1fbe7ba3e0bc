<?php session_start();

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/stream.php';
require_once __ROOT__ . '/src/general/preparar.php';

$streams = array();
$showeditstream = 0;

/* BEGIN sub_addstream */
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addstream'])) {
	try {
		$newstream = new Stream;
		$newstream->nombre = limpiar_datos($_POST['mdladdstream_nombre']);
		$newstream->link = limpiar_datos($_POST['mdladdstream_link']);
		$newstream->prio = limpiar_datos($_POST['mdladdstream_prio']);
		$newstream->add($conexion);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
/* END sub_addstream */
/* BEGIN sub_showeditstream */
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_showeditstream'])) {
	try {
		$id_stream = limpiar_datos($_POST['selectedstream']);
		
		$modstream = Stream::get($id_stream,$conexion);

		$showeditstream = 1;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
/* END sub_showeditstream */
/* BEGIN sub_editstream */
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editstream'])) {
	try {
		$modstream = new Stream;
		$modstream->id_stream = limpiar_datos($_POST['selectedstream']);
		$modstream->nombre = limpiar_datos($_POST['mdleditstream_nombre']);
		$modstream->link = limpiar_datos($_POST['mdleditstream_link']);
		$modstream->prio = limpiar_datos($_POST['mdleditstream_prio']);
		$modstream->modify($conexion);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
/* END sub_editstream */
/* BEGIN sub_delstream */
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delstream'])) {
	try {
		$id_stream = limpiar_datos($_POST['mdldelstream_id_stream']);
		
		Stream::delete($id_stream,$conexion);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
/* END sub_delstream */

try {
	$streams = Stream::get_list($conexion);

} catch (Exception $e) {
	$error_display = 'show';
	$error_text = $e->getMessage();
}

require_once __ROOT__ . '/views/lstreams.view.php';

?>
