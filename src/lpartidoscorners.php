<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/partidoapuestacomparar.php';
require_once __ROOT__ . '/src/general/preparar.php';

$tabselected = 1;

//default values:
$valuetoyellow = PartidoCorner::VALUETOYELLOW;
$valuetogreen = PartidoCorner::VALUETOGREEN;
$masxtotalcorners = PartidoCorner::DEFAULT_MASXTOTALCORNERS;
$menosxtotalcorners = PartidoCorner::DEFAULT_MENOSXTOTALCORNERS;
$masxcornershome = PartidoCorner::DEFAULT_MASXCORNERSHOME;
$menosxcornershome = PartidoCorner::DEFAULT_MENOSXCORNERSHOME;
$masxcornersaway = PartidoCorner::DEFAULT_MASXCORNERSAWAY;
$menosxcornersaway = PartidoCorner::DEFAULT_MENOSXCORNERSAWAY;
$numeropartidos = PartidoCorner::constructArrayNumeroPartidos();
$probabilidades_totalcorners = PartidoCorner::constructArrayProbabilidades();
$probabilidades_mascorners = PartidoCorner::constructArrayProbabilidades();
$probabilidades_cornershome = PartidoCorner::constructArrayProbabilidades();
$probabilidades_cornersaway = PartidoCorner::constructArrayProbabilidades();
$prom_corners = PartidoCorner::constructArrayPromCorner();
$promcorners_cumple = PartidoCorner::constructArrayPromCornerCumple();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

        if (isset($_SESSION['idpartido'])) {
            $idpartido = $_SESSION['idpartido'];
        
            // logic:
            // obtener variables para este partido:
            $idpartidoordenado = ordena($idpartido);

            if(isset($_SESSION["loaded_$idpartidoordenado"])){
                $masxtotalcorners = $_SESSION["masxtotalcorners_$idpartidoordenado"];
                $menosxtotalcorners = $_SESSION["menosxtotalcorners_$idpartidoordenado"];
                $masxcornershome = $_SESSION["masxcornershome_$idpartidoordenado"];
                $menosxcornershome = $_SESSION["menosxcornershome_$idpartidoordenado"];
                $masxcornersaway = $_SESSION["masxcornersaway_$idpartidoordenado"];
                $menosxcornersaway = $_SESSION["menosxcornersaway_$idpartidoordenado"];
            }
        
            unset($_SESSION['idpartido']);
        } else {
            header('Location: lpartidos');
        }

        if (isset($_GET['ip'])) {
            $success_display = 'show';
            $success_text = 'El partido ha sido ingresado.';
        }
        if (isset($_GET['i'])) {
            $success_display = 'show';
            $success_text = 'La apuesta ha sido ingresada.';
        }

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $idpartido = limpiar_datos($_POST['idpartido']);
        $tabselected = limpiar_datos($_POST["tabselected"]);
        $idapuestatipocombinada = limpiar_datos($_POST['idapuestatipocombinada']);

        //variables corners digitados
        $masxtotalcorners = limpiar_datos($_POST['masxtotalcorners']);
        $menosxtotalcorners = limpiar_datos($_POST['menosxtotalcorners']);        
        $masxcornershome = limpiar_datos($_POST['masxcornershome']);
        $menosxcornershome = limpiar_datos($_POST['menosxcornershome']);
        $masxcornersaway = limpiar_datos($_POST['masxcornersaway']);
        $menosxcornersaway = limpiar_datos($_POST['menosxcornersaway']);

        //guardar las variables en session para que no se pierdan:
        $idpartidoordenado = ordena($idpartido);

        $_SESSION["loaded_$idpartidoordenado"] = 1;
        $_SESSION["masxtotalcorners_$idpartidoordenado"] = $masxtotalcorners;
        $_SESSION["menosxtotalcorners_$idpartidoordenado"] = $menosxtotalcorners;
        $_SESSION["masxcornershome_$idpartidoordenado"] = $masxcornershome;
        $_SESSION["menosxcornershome_$idpartidoordenado"] = $menosxcornershome;
        $_SESSION["masxcornersaway_$idpartidoordenado"] = $masxcornersaway;
        $_SESSION["menosxcornersaway_$idpartidoordenado"] = $menosxcornersaway;


    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_subir
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_subir'])) {
    try {
        $method_sub_subir = 1;

        $conexion->beginTransaction();
        
        $archivo = $_FILES['archivo']['tmp_name'];
        $archivocsv = file($archivo);

        Partido::addCorners($idpartido, $archivocsv, $conexion);
    
        $conexion->commit();

        $success_display = 'show';
        $success_text = 'Los corners han sido agregados.';
    
    } catch (Exception $e) {
        $conexion->rollback();
    
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_subir
#region sub_apostaropt1_totalcorners
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostaropt1_totalcorners'])) {
    try {
        $method_sub_apostaropt1_totalcorners = 1;

        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASXTOTALCORNERS);
        $_SESSION['valorapuesta'] = $masxtotalcorners;

        header('Location: ipartidoapuesta');
        exit();        

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostaropt1_totalcorners
#region sub_apostaropt2_totalcorners
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostaropt2_totalcorners'])) {
    try {
        $method_sub_apostaropt2_totalcorners = 1;

        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_MENOSXTOTALCORNERS);
        $_SESSION['valorapuesta'] = $menosxtotalcorners;

        header('Location: ipartidoapuesta');
        exit();          

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostaropt2_totalcorners
#region sub_compararopt1_totalcorners
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_compararopt1_totalcorners'])) {
    try {
        $method_sub_compararopt1_totalcorners = 1;
        
        $param = array();
        $param['idpartido'] = $idpartido;
        $param['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASXTOTALCORNERS);
        $param['valorapuesta'] = $masxtotalcorners;
        PartidoApuestaComparar::addComparar($param, $conexion);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido ingresada para comparar.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_compararopt1_totalcorners
#region sub_compararopt2_totalcorners
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_compararopt2_totalcorners'])) {
    try {
        $method_sub_compararopt2_totalcorners = 1;
        
        $param = array();
        $param['idpartido'] = $idpartido;
        $param['idtipoapuesta'] = desordena(ApuestaTipo::ID_MENOSXTOTALCORNERS);
        $param['valorapuesta'] = $menosxtotalcorners;
        PartidoApuestaComparar::addComparar($param, $conexion);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido ingresada para comparar.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_compararopt2_totalcorners
#region sub_apostaropt1_mascorners
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostaropt1_mascorners'])) {
    try {
        $method_sub_apostaropt1_mascorners = 1;
        
        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASCORNERSHOME);
        $_SESSION['valorapuesta'] = 1;

        header('Location: ipartidoapuesta');
        exit(); 

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostaropt1_mascorners
#region sub_apostaropt2_mascorners
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostaropt2_mascorners'])) {
    try {
        $method_sub_apostaropt2_mascorners = 1;
        
        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASCORNERSAWAY);
        $_SESSION['valorapuesta'] = 2;

        header('Location: ipartidoapuesta');
        exit(); 

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostaropt2_mascorners
#region sub_compararopt1_mascorners
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_compararopt1_mascorners'])) {
    try {
        $method_sub_compararopt1_mascorners = 1;
        
        $param = array();
        $param['idpartido'] = $idpartido;
        $param['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASCORNERSHOME);
        $param['valorapuesta'] = 1;
        PartidoApuestaComparar::addComparar($param, $conexion);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido ingresada para comparar.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_compararopt1_mascorners
#region sub_compararopt2_mascorners
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_compararopt2_mascorners'])) {
    try {
        $method_sub_compararopt2_mascorners = 1;
        
        $param = array();
        $param['idpartido'] = $idpartido;
        $param['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASCORNERSAWAY);
        $param['valorapuesta'] = 2;
        PartidoApuestaComparar::addComparar($param, $conexion);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido ingresada para comparar.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_compararopt2_mascorners
#region sub_apostaropt1_cornershome
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostaropt1_cornershome'])) {
    try {
        $method_sub_apostaropt1_cornershome = 1;
        
        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASXCORNERSHOME);
        $_SESSION['valorapuesta'] = $masxcornershome;

        header('Location: ipartidoapuesta');
        exit(); 

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostaropt1_cornershome
#region sub_apostaropt2_cornershome
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostaropt2_cornershome'])) {
    try {
        $method_sub_apostaropt2_cornershome = 1;
        
        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_MENOSXCORNERSHOME);
        $_SESSION['valorapuesta'] = $menosxcornershome;

        header('Location: ipartidoapuesta');
        exit(); 

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostaropt2_cornershome
#region sub_compararopt1_cornershome
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_compararopt1_cornershome'])) {
    try {
        $method_sub_compararopt1_cornershome = 1;
        
        $param = array();
        $param['idpartido'] = $idpartido;
        $param['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASXCORNERSHOME);
        $param['valorapuesta'] = $masxcornershome;
        PartidoApuestaComparar::addComparar($param, $conexion);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido ingresada para comparar.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_compararopt1_cornershome
#region sub_compararopt2_cornershome
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_compararopt2_cornershome'])) {
    try {
        $method_sub_compararopt2_cornershome = 1;
        
        $param = array();
        $param['idpartido'] = $idpartido;
        $param['idtipoapuesta'] = desordena(ApuestaTipo::ID_MENOSXCORNERSHOME);
        $param['valorapuesta'] = $menosxcornershome;
        PartidoApuestaComparar::addComparar($param, $conexion);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido ingresada para comparar.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_compararopt2_cornershome
#region sub_apostaropt1_cornersaway
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostaropt1_cornersaway'])) {
    try {
        $method_sub_apostaropt1_cornersaway = 1;
        
        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASXCORNERSAWAY);
        $_SESSION['valorapuesta'] = $masxcornersaway;

        header('Location: ipartidoapuesta');
        exit(); 

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostaropt1_cornersaway
#region sub_apostaropt2_cornersaway
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_apostaropt2_cornersaway'])) {
    try {
        $method_sub_apostaropt2_cornersaway = 1;
        
        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = desordena(ApuestaTipo::ID_MENOSXCORNERSAWAY);
        $_SESSION['valorapuesta'] = $menosxcornersaway;

        header('Location: ipartidoapuesta');
        exit(); 

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_apostaropt2_cornersaway
#region sub_compararopt1_cornersaway
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_compararopt1_cornersaway'])) {
    try {
        $method_sub_compararopt1_cornersaway = 1;
        
        $param = array();
        $param['idpartido'] = $idpartido;
        $param['idtipoapuesta'] = desordena(ApuestaTipo::ID_MASXCORNERSAWAY);
        $param['valorapuesta'] = $masxcornersaway;
        PartidoApuestaComparar::addComparar($param, $conexion);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido ingresada para comparar.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_compararopt1_cornersaway
#region sub_compararopt2_cornersaway
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_compararopt2_cornersaway'])) {
    try {
        $method_sub_compararopt2_cornersaway = 1;
        
        $param = array();
        $param['idpartido'] = $idpartido;
        $param['idtipoapuesta'] = desordena(ApuestaTipo::ID_MENOSXCORNERSAWAY);
        $param['valorapuesta'] = $menosxcornersaway;
        PartidoApuestaComparar::addComparar($param, $conexion);

        $success_display = 'show';
        $success_text = 'La apuesta ha sido ingresada para comparar.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_compararopt2_cornersaway
#region sub_addcombinada
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addcombinada'])) {
    try {
        $method_sub_addcombinada = 1;
        
        $_SESSION['idpartido'] = $idpartido;
        $_SESSION['idtipoapuesta'] = $idapuestatipocombinada;
        $_SESSION['valorapuesta'] = 0;

        header('Location: ipartidoapuestacombinada');
        exit(); 

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addcombinada
#region sub_addotraapuesta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addotraapuesta'])) {
    try {
        $method_sub_addotraapuesta = 1;

        $_SESSION['idpartido'] = $idpartido;

        $nomotrasapuestatipo = limpiar_datos($_POST['nomotrasapuestatipo']);
        $valorotraapuesta = limpiar_datos($_POST['valorotraapuesta']);

        $idapuestatipo = ApuestaTipo::getIdByNombre($nomotrasapuestatipo, $conexion);

        $_SESSION['idtipoapuesta'] = $idapuestatipo;
        $_SESSION['valorapuesta'] = $valorotraapuesta;

        header('Location: ipartidootrasapuestas');
        exit();         

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_addotraapuesta
#region try
try {
    $method_try = 1;

    $modpartido = Partido::get($idpartido, $conexion);
    $partidoscorners = PartidoCorner::getList($idpartido, $conexion);
    $apuestastipos_combinadas = ApuestaTipo::getListSoloIsCombinada($conexion);

    //calcular todo:
    $param = array();
    $param['idpartido'] = $idpartido;
    $numeropartidos = PartidoCorner::calcularNumeroPartidos($param, $conexion);

    if(count($partidoscorners) > 0){
        //obtener probabilidades
        $param = array();
        $param['idpartido'] = $idpartido;
        $param['numeropartidos'] = $numeropartidos;
        $param['masxtotalcorners'] = $masxtotalcorners;
        $param['menosxtotalcorners'] = $menosxtotalcorners;
        $param['masxcornershome'] = $masxcornershome;
        $param['menosxcornershome'] = $menosxcornershome;
        $param['masxcornersaway'] = $masxcornersaway;
        $param['menosxcornersaway'] = $menosxcornersaway;
        $probabilidades = PartidoCorner::calcularProbabilidades($param, $conexion);
        $probabilidades_totalcorners = $probabilidades['totalcorners'];
        $probabilidades_mascorners = $probabilidades['mascorners'];
        $probabilidades_cornershome = $probabilidades['cornershome'];
        $probabilidades_cornersaway = $probabilidades['cornersaway'];
        $prom_corners = $probabilidades['prom_corners'];

        $param = array();
        $param['numeropartidos'] = $numeropartidos;
        $param['prom_corners'] = $prom_corners;
        $prom_corners = PartidoCorner::definirPromedioCorners($param);

        $param['prom_corners'] = $prom_corners;
        $param['probabilidades'] = $probabilidades;
        $param['masxtotalcorners'] = $masxtotalcorners;
        $param['menosxtotalcorners'] = $menosxtotalcorners;
        $param['masxcornershome'] = $masxcornershome;
        $param['menosxcornershome'] = $menosxcornershome;
        $param['masxcornersaway'] = $masxcornersaway;
        $param['menosxcornersaway'] = $menosxcornersaway;
        $promcorners_cumple = PartidoCorner::definirPromedioCornerCumple($param);
    }

    //BEGIN obtener win ratio de los tipos de apuesta
    $winratio_mas_totalcorners = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_MASXTOTALCORNERS), $conexion);
    $winratio_menos_totalcorners = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_MENOSXTOTALCORNERS), $conexion);
    $winratio_mascorners_home = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_MASCORNERSHOME), $conexion);
    $winratio_mascorners_away = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_MASCORNERSAWAY), $conexion);
    $winratio_mas_cornershome = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_MASXCORNERSHOME), $conexion);
    $winratio_menos_cornershome = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_MENOSXCORNERSAWAY), $conexion);
    $winratio_mas_cornersaway = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_MASXCORNERSAWAY), $conexion);
    $winratio_menos_cornersaway = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_MENOSXCORNERSAWAY), $conexion);
    $winratio_combinada2 = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_COMBINADA2), $conexion);
    $winratio_combinada3 = PartidoApuesta::createReporteWinRatioApuestaTipo(desordena(ApuestaTipo::ID_COMBINADA3), $conexion);
    //END obtener win ratio de los tipos de apuesta

    //obtener winratio del pais
    //$winratio_pais = PartidoApuesta::createReporteWinRatioPais($modpartido->pais, $conexion);

    
    $partidosapuestas = PartidoApuesta::getListByIdPartido($idpartido, $conexion);
    $apuestastipos = ApuestaTipo::getList(array(),$conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lpartidoscorners.view.php';

?>