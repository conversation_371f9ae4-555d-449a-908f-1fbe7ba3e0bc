<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/note.php';
require_once __ROOT__ . '/src/general/preparar.php';

$notes = array();
$update_list = 0;
$sel_note = new Note;
$sel_note->title = "Select a note";
$sel_note->detail = "...";
$sel_note->source1title = "NA";
$sel_note->source1ref = "#";
$sel_note->source2title = "NA";
$sel_note->source2ref = "#";
$search = '';
$id_note = '';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		if (isset($_GET['add'])) {
			$success_display = 'show';
			$success_text = 'Note has been saved.';

			$search = limpiar_datos($_GET['search']);
			$id_note = limpiar_datos($_GET['in']);

			$sel_note = Note::get($id_note,$conexion);

			$update_list = 1;
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$id_note = limpiar_datos($_POST['id_note']);
		$search = trim(limpiar_datos($_POST['search']));

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion postsolo
#region search
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_search'])) {
	try {
		validar_textovacio($search, 'Specify search');

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion
#region select
elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_select'])) {
	try {
		$sel_note = Note::get($id_note,$conexion);

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion
#region sub_del
elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_del'])) {
	try {
		Note::delete($id_note,$conexion);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_del
#region sub_edit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_edit'])) {
	try {
		$title = limpiar_datos($_POST['title']);
		$detail = limpiar_datos($_POST['detail']);
		$source1ref = '';
		$source1title = '';
		$source2ref = '';
		$source2title = '';
		$tags = limpiar_datos($_POST['tags']);

		validar_textovacio($id_note, 'Specify id_note');
		validar_textovacio($title, 'Specify title');
		validar_textovacio($detail, 'Specify detail');

		$edit_note = new Note;
		$edit_note->id_note = $id_note;
		$edit_note->title = strtolower($title);
		$edit_note->detail = $detail;
		$edit_note->source1ref = ($source1ref == "") ? '#' : $source1ref;
		$edit_note->source1title = ($source1title == "") ? 'NA' : $source1title;
		$edit_note->source2ref = ($source2ref == "") ? '#' : $source2ref;
		$edit_note->source2title = ($source2title == "") ? 'NA' : $source2title;
		$edit_note->tags = strtolower($tags);
		$edit_note->modify($conexion);

		$success_display = 'show';
		$success_text = 'The note has been modified.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_edit
#region try
try {
	if(!empty($search)){
		$notes = Note::get_list($search,$conexion);
	}
	if (!empty($id_note)) {
		$sel_note = Note::get($id_note,$conexion);
	}
} catch (Exception $e) {
	$error_display = 'show';
	$error_text = $e->getMessage();
}
#endregion

require_once __ROOT__ . '/views/lnotes.view.php';

?>
