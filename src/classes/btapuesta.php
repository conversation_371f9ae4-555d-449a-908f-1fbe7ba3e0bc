<?php

require_once __ROOT__ . '/src/classes/btapuestaestado.php';
require_once __ROOT__ . '/src/classes/source.php';
require_once __ROOT__ . '/src/classes/channel.php';
require_once __ROOT__ . '/src/classes/equipo.php';
require_once __ROOT__ . '/src/classes/torneo.php';
require_once __ROOT__ . '/src/classes/stakazo.php';

class BtApuesta
{
    public string $id;
    public string $fecha;
    public BtApuestaEstado $btestado;
    public Source $source;
    public Channel $channel;
    public float $stake;
    public float $stake_source;
    public Equipo $equipo_home;
    public Equipo $equipo_away;
    public Torneo $torneo;
    public ?string $fecha_partido;
    public ?string $fecha_partido_hora;
    public ?string $fecha_partido_texto;
    public ?string $fecha_partido_texto_mini;
    public string $tipo_apuesta;
    public string $tipo_apuesta_add;
    public string $tipo_apuesta_add_texto;
    public string $tipo_apuesta_add2;
    public float $apostado;
    public float $cuota_source;
    public float $cuota_grupo;
    public float $cuota_real;
    public float $bankroll;
    public Stakazo $stakazo;
    public float $probabilidades;
    public int $ganado;
    public int $usado;
    public string $usado_texto;
    public float $recibido;
    public float $profit;
    public string $id_apuesta_combinada;
    public int $es_combinada;
    public int $estado;
    public string $ganado_icono;
    public array $combinada_items;
    private string $bd_table                = 'bt_apuestas';
    private string $bd_alias                = 'btap';
    private string $bd_id                   = 'id';
    private string $bd_fecha                = 'fecha';
    private string $bd_id_estado            = 'id_estado';
    private string $bd_f_estado_nombre      = 'estado_nombre';
    private string $bd_f_estado_color_bg    = 'estado_color_bg';
    private string $bd_id_source            = 'id_source';
    private string $bd_f_source_nombre      = 'source_nombre';
    private string $bd_id_channel           = 'id_channel';
    private string $bd_f_channel_nombre     = 'channel_nombre';
    private string $bd_f_channel_color_text = 'channel_color_text';
    private string $bd_stake                = 'stake';
    private string $bd_stake_source         = 'stake_source';
    private string $bd_id_equipo_home       = 'id_equipo_home';
    private string $bd_f_equipo_home_nombre = 'equipo_home_nombre';
    private string $bd_id_equipo_away       = 'id_equipo_away';
    private string $bd_f_equipo_away_nombre = 'equipo_away_nombre';
    private string $bd_id_torneo            = 'id_torneo';
    private string $bd_f_torneo_nombre      = 'torneo_nombre';
    private string $bd_fecha_partido        = 'fecha_partido';
    private string $bd_tipo_apuesta         = 'tipo_apuesta';
    private string $bd_tipo_apuesta_add     = 'tipo_apuesta_add';
    private string $bd_tipo_apuesta_add2    = 'tipo_apuesta_add2';
    private string $bd_apostado             = 'apostado';
    private string $bd_cuota_source         = 'cuota_source';
    private string $bd_cuota_grupo          = 'cuota_grupo';
    private string $bd_cuota_real           = 'cuota_real';
    private string $bd_bankroll             = 'bankroll';
    private string $bd_id_stakazo           = 'id_stakazo';
    private string $bd_f_stakazo_nombre     = 'stakazo_nombre';
    private string $bd_probabilidades       = 'probabilidades';
    private string $bd_ganado               = 'ganado';
    private string $bd_usado                = 'usado';
    private string $bd_recibido             = 'recibido';
    private string $bd_profit               = 'profit';
    private string $bd_id_apuesta_combinada = 'id_apuesta_combinada';
    private string $bd_es_combinada         = 'es_combinada';
    private string $bd_estado               = 'estado';
    const TEXTO_COMBINADA = '-COMBINADA-';

    function __construct()
    {
        $this->id                = '';
        $this->fecha             = '';
        $this->btestado          = new BtApuestaEstado();
        $this->source            = new Source();
        $this->channel           = new Channel();
        $this->stake             = 0;
        $this->stake_source      = 0;
        $this->equipo_home       = new Equipo();
        $this->equipo_away       = new Equipo();
        $this->torneo            = new Torneo();
        $this->fecha_partido     = '';
        $this->fecha_partido_hora     = '';
        $this->fecha_partido_texto     = '';
        $this->fecha_partido_texto_mini     = '';
        $this->tipo_apuesta      = '';
        $this->tipo_apuesta_add  = '';
        $this->tipo_apuesta_add_texto  = '';
        $this->tipo_apuesta_add2 = '';
        $this->apostado          = 0;
        $this->cuota_source      = 0;
        $this->cuota_grupo       = 0;
        $this->cuota_real        = 0;
        $this->bankroll          = 0;
        $this->stakazo           = new Stakazo;
        $this->probabilidades    = 0;
        $this->ganado            = 0;
        $this->usado             = 0;
        $this->usado_texto       = '';
        $this->recibido          = 0;
        $this->profit            = 0;
        $this->id_apuesta_combinada = 0;
        $this->es_combinada         = 0;
        $this->estado               = 0;
        $this->ganado_icono         = '';
        $this->combinada_items   = array();
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado, $conexion): self
    {
        try {
            $cq = new self;

            $objeto                      = new self;
            $objeto->id                  = desordena($resultado[$cq->bd_id]);
            $objeto->fecha               = $resultado[$cq->bd_fecha];
            $objeto->btestado->id        = desordena($resultado[$cq->bd_id_estado]);
            $objeto->btestado->nombre    = (isset($resultado["$cq->bd_f_estado_nombre"])) ? $resultado["$cq->bd_f_estado_nombre"] : "";
            $objeto->btestado->color_bg  = (isset($resultado["$cq->bd_f_estado_color_bg"])) ? $resultado["$cq->bd_f_estado_color_bg"] : "";
            $objeto->source->id          = desordena($resultado[$cq->bd_id_source]);
            $objeto->source->nombre      = (isset($resultado["$cq->bd_f_source_nombre"])) ? $resultado["$cq->bd_f_source_nombre"] : "";
            $objeto->channel->id         = desordena($resultado[$cq->bd_id_channel]);
            $objeto->channel->nombre     = (isset($resultado["$cq->bd_f_channel_nombre"])) ? $resultado["$cq->bd_f_channel_nombre"] : "";
            $objeto->channel->color_text = (isset($resultado["$cq->bd_f_channel_color_text"])) ? $resultado["$cq->bd_f_channel_color_text"] : "";
            $objeto->stake               = $resultado[$cq->bd_stake];
            $objeto->stake_source        = $resultado[$cq->bd_stake_source];
            $objeto->equipo_home->id     = desordena($resultado[$cq->bd_id_equipo_home]);
            $objeto->equipo_home->nombre = (isset($resultado["$cq->bd_f_equipo_home_nombre"])) ? $resultado["$cq->bd_f_equipo_home_nombre"] : "";
            $objeto->equipo_away->id     = desordena($resultado[$cq->bd_id_equipo_away]);
            $objeto->equipo_away->nombre = (isset($resultado["$cq->bd_f_equipo_away_nombre"])) ? $resultado["$cq->bd_f_equipo_away_nombre"] : "";
            $objeto->torneo->id          = desordena($resultado[$cq->bd_id_torneo]);
            $objeto->torneo->nombre      = (isset($resultado["$cq->bd_f_torneo_nombre"])) ? $resultado["$cq->bd_f_torneo_nombre"] : "";
            $objeto->fecha_partido       = $resultado[$cq->bd_fecha_partido];
            $objeto->fecha_partido_hora  = date('H:i', strtotime($objeto->fecha_partido));
            $objeto->tipo_apuesta        = $resultado[$cq->bd_tipo_apuesta];
            $objeto->tipo_apuesta_add    = $resultado[$cq->bd_tipo_apuesta_add];
            $objeto->tipo_apuesta_add2   = $resultado[$cq->bd_tipo_apuesta_add2];
            $objeto->apostado            = $resultado[$cq->bd_apostado];
            $objeto->cuota_source        = $resultado[$cq->bd_cuota_source];
            $objeto->cuota_grupo         = $resultado[$cq->bd_cuota_grupo];
            $objeto->cuota_real          = $resultado[$cq->bd_cuota_real];
            $objeto->bankroll            = $resultado[$cq->bd_bankroll];
            $objeto->stakazo->id         = desordena($resultado[$cq->bd_id_stakazo]);
            $objeto->stakazo->nombre     = (isset($resultado["$cq->bd_f_stakazo_nombre"])) ? $resultado["$cq->bd_f_stakazo_nombre"] : "";
            $objeto->probabilidades      = $resultado[$cq->bd_probabilidades];
            $objeto->ganado              = $resultado[$cq->bd_ganado];
            $objeto->usado               = $resultado[$cq->bd_usado];
            $objeto->recibido            = $resultado[$cq->bd_recibido];
            $objeto->profit              = $resultado[$cq->bd_profit];
            $objeto->id_apuesta_combinada = desordena($resultado[$cq->bd_id_apuesta_combinada]);
            $objeto->es_combinada         = $resultado[$cq->bd_es_combinada];
            $objeto->estado               = $resultado[$cq->bd_estado];

            //texto si la apuesta se uso para mi cuenta personal
            if($objeto->usado == 1){
                $objeto->usado_texto = 'SI';

            } else{
                $objeto->usado_texto = 'NO';
            }

            //construir texto de la fecha - hora del partido
            if (date('Y-m-d', strtotime($objeto->fecha_partido)) == create_date()) {
                $objeto->fecha_partido_texto = 'Hoy';
            } else{
                $objeto->fecha_partido_texto = $objeto->fecha_partido;
            }

            $objeto->fecha_partido_texto .= ' - ' . date('h:i A', strtotime($objeto->fecha_partido));
            
            $horas_faltan = getDateDiffHours(create_datetime(), $objeto->fecha_partido);

            if($horas_faltan < 0){
                $objeto->fecha_partido_texto .= ' (Finalizado)';
                $objeto->fecha_partido_texto_mini .= '-FINALIZADO-';

            } elseif($horas_faltan == 0){
                $objeto->fecha_partido_texto .= ' (En menos de una hora)';
                $objeto->fecha_partido_texto_mini .= '< 1 HORA';

            } else{
                if($horas_faltan == 1){
                    $hora_texto = 'hora';

                } else{
                    $hora_texto = 'horas';
                }

                $objeto->fecha_partido_texto .= ' (En aprox. ' . $horas_faltan . ' ' . $hora_texto . ')';
                $objeto->fecha_partido_texto_mini .= '~' . $horas_faltan . ' ' . mb_strtoupper($hora_texto);
            }

            //contruir texto para algunos tipos de apuesta add
            if($objeto->tipo_apuesta == 'TIEMPO REGLAMENTARIO' || $objeto->tipo_apuesta == 'DESCANSO' || $objeto->tipo_apuesta == 'APUESTA SIN EMPATE'){
                if($objeto->tipo_apuesta_add == 'HOME'){
                    $objeto->tipo_apuesta_add_texto = 'GANA ' . $objeto->equipo_home->nombre;
                } else{
                    $objeto->tipo_apuesta_add_texto = 'GANA ' . $objeto->equipo_away->nombre;
                }

            } elseif($objeto->tipo_apuesta == 'DOBLE OPORTUNIDAD'){
                if($objeto->tipo_apuesta_add == 'HOME O EMPATE'){
                    $objeto->tipo_apuesta_add_texto = 'GANA ' . $objeto->equipo_home->nombre . ' -O- EMPATA';

                } elseif($objeto->tipo_apuesta_add == 'AWAY O EMPATE'){
                    $objeto->tipo_apuesta_add_texto = 'GANA ' . $objeto->equipo_away->nombre . ' -O- EMPATA';

                } else{
                    $objeto->tipo_apuesta_add_texto = $objeto->tipo_apuesta_add;
                }                
            } else{
                $objeto->tipo_apuesta_add_texto = $objeto->tipo_apuesta_add;
            }

            //asignar icono de apuesta
            if($objeto->ganado == 1){
                $objeto->ganado_icono = '<i class="fas fa-check-circle fa-1_5x text-success"></i>';

            } elseif($objeto->ganado == 0){
                $objeto->ganado_icono = '<i class="fas fa-times-circle fa-1_5x text-danger"></i>';

            } else{
                $objeto->ganado_icono = '<i class="fas fa-exclamation-circle fa-1_5x text-warning"></i>';
            }

            //buscar los items si es una apuesta combinada
            if($objeto->es_combinada == 1){                
                $objeto->combinada_items = self::get_list(array('id_apuesta_combinada' => $objeto->id), $conexion);
            }

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, $conexion): self
    {
        try {
            $cq       = new self;
            $cqa      = $cq->bd_alias;
            $cq_sta   = new Stakazo();
            $cq_sta_a = $cq_sta->bd_alias;
            $cq_cha   = new Channel();
            $cq_cha_a = $cq_cha->bd_alias;
            $cq_est   = new BtApuestaEstado();
            $cq_est_a = $cq_est->bd_alias;
            $cq_eq    = new Equipo();
            $cq_eq_a  = $cq_eq->bd_alias;
            $cq_eq2   = new Equipo();
            $cq_eq2_a = $cq_eq->bd_alias2;
            $cq_sou   = new Source();
            $cq_sou_a = $cq_sou->bd_alias;
            $cq_tor   = new Torneo();
            $cq_tor_a = $cq_tor->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "  ,$cq_sta_a.$cq_sta->bd_nombre $cq->bd_f_stakazo_nombre ";
            $query .= "  ,$cq_cha_a.$cq_cha->bd_nombre $cq->bd_f_channel_nombre ";
            $query .= "  ,$cq_cha_a.$cq_cha->bd_color_text $cq->bd_f_channel_color_text ";
            $query .= "  ,$cq_est_a.$cq_est->bd_nombre $cq->bd_f_estado_nombre ";
            $query .= "  ,$cq_est_a.$cq_est->bd_color_bg $cq->bd_f_estado_color_bg ";
            $query .= "  ,$cq_eq_a.$cq_eq->bd_nombre $cq->bd_f_equipo_home_nombre ";
            $query .= "  ,$cq_eq2_a.$cq_eq->bd_nombre $cq->bd_f_equipo_away_nombre ";
            $query .= "  ,$cq_sou_a.$cq_sou->bd_nombre $cq->bd_f_source_nombre ";
            $query .= "  ,$cq_tor_a.$cq_tor->bd_nombre $cq->bd_f_torneo_nombre ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "INNER JOIN $cq_sta->bd_table $cq_sta_a ";
            $query .= "  ON ($cq_sta_a.$cq_sta->bd_id = $cqa.$cq->bd_id_stakazo) ";
            $query .= "INNER JOIN $cq_cha->bd_table $cq_cha_a ";
            $query .= "  ON ($cq_cha_a.$cq_cha->bd_id = $cqa.$cq->bd_id_channel) ";
            $query .= "INNER JOIN $cq_est->bd_table $cq_est_a ";
            $query .= "  ON ($cq_est_a.$cq_est->bd_id = $cqa.$cq->bd_id_estado) ";
            $query .= "INNER JOIN $cq_eq->bd_table $cq_eq_a ";
            $query .= "  ON ($cq_eq_a.$cq_eq->bd_id = $cqa.$cq->bd_id_equipo_home) ";
            $query .= "INNER JOIN $cq_eq2->bd_table $cq_eq2_a ";
            $query .= "  ON ($cq_eq2_a.$cq_eq2->bd_id = $cqa.$cq->bd_id_equipo_away) ";
            $query .= "INNER JOIN $cq_sou->bd_table $cq_sou_a ";
            $query .= "  ON ($cq_sou_a.$cq_sou->bd_id = $cqa.$cq->bd_id_source) ";
            $query .= "INNER JOIN $cq_tor->bd_table $cq_tor_a ";
            $query .= "  ON ($cq_tor_a.$cq_tor->bd_id = $cqa.$cq->bd_id_torneo) ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado, $conexion);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_list($paramref, $conexion): array
    {
        try {
            $diff_id          = (isset($paramref["diff_id"])) ? $paramref["diff_id"] : "";
            $fecha            = (isset($paramref["fecha"])) ? $paramref["fecha"] : "";
            $id_equipo_home   = (isset($paramref["id_equipo_home"])) ? $paramref["id_equipo_home"] : "";
            $id_equipo_away   = (isset($paramref["id_equipo_away"])) ? $paramref["id_equipo_away"] : "";
            $tipo_apuesta     = (isset($paramref["tipo_apuesta"])) ? $paramref["tipo_apuesta"] : "";
            $tipo_apuesta_add = (isset($paramref["tipo_apuesta_add"])) ? $paramref["tipo_apuesta_add"] : "";
            $id_apuesta_combinada = (isset($paramref["id_apuesta_combinada"])) ? $paramref["id_apuesta_combinada"] : "";
            $id_source            = (isset($paramref["id_source"])) ? $paramref["id_source"] : "";

            $cq       = new self;
            $cqa      = $cq->bd_alias;
            $cq_sta   = new Stakazo();
            $cq_sta_a = $cq_sta->bd_alias;
            $cq_cha   = new Channel();
            $cq_cha_a = $cq_cha->bd_alias;
            $cq_est   = new BtApuestaEstado();
            $cq_est_a = $cq_est->bd_alias;
            $cq_eq    = new Equipo();
            $cq_eq_a  = $cq_eq->bd_alias;
            $cq_eq2   = new Equipo();
            $cq_eq2_a = $cq_eq->bd_alias2;
            $cq_sou   = new Source();
            $cq_sou_a = $cq_sou->bd_alias;
            $cq_tor   = new Torneo();
            $cq_tor_a = $cq_tor->bd_alias;

            $query = "SELECT ";
            $query .= "   $cqa.* ";
            $query .= "  ,$cq_sta_a.$cq_sta->bd_nombre $cq->bd_f_stakazo_nombre ";
            $query .= "  ,$cq_cha_a.$cq_cha->bd_nombre $cq->bd_f_channel_nombre ";
            $query .= "  ,$cq_cha_a.$cq_cha->bd_color_text $cq->bd_f_channel_color_text ";
            $query .= "  ,$cq_est_a.$cq_est->bd_nombre $cq->bd_f_estado_nombre ";
            $query .= "  ,$cq_est_a.$cq_est->bd_color_bg $cq->bd_f_estado_color_bg ";
            $query .= "  ,$cq_eq_a.$cq_eq->bd_nombre $cq->bd_f_equipo_home_nombre ";
            $query .= "  ,$cq_eq2_a.$cq_eq->bd_nombre $cq->bd_f_equipo_away_nombre ";
            $query .= "  ,$cq_sou_a.$cq_sou->bd_nombre $cq->bd_f_source_nombre ";
            $query .= "  ,$cq_tor_a.$cq_tor->bd_nombre $cq->bd_f_torneo_nombre ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "INNER JOIN $cq_sta->bd_table $cq_sta_a ";
            $query .= "  ON ($cq_sta_a.$cq_sta->bd_id = $cqa.$cq->bd_id_stakazo) ";
            $query .= "INNER JOIN $cq_cha->bd_table $cq_cha_a ";
            $query .= "  ON ($cq_cha_a.$cq_cha->bd_id = $cqa.$cq->bd_id_channel) ";
            $query .= "INNER JOIN $cq_est->bd_table $cq_est_a ";
            $query .= "  ON ($cq_est_a.$cq_est->bd_id = $cqa.$cq->bd_id_estado) ";
            $query .= "INNER JOIN $cq_eq->bd_table $cq_eq_a ";
            $query .= "  ON ($cq_eq_a.$cq_eq->bd_id = $cqa.$cq->bd_id_equipo_home) ";
            $query .= "INNER JOIN $cq_eq2->bd_table $cq_eq2_a ";
            $query .= "  ON ($cq_eq2_a.$cq_eq2->bd_id = $cqa.$cq->bd_id_equipo_away) ";
            $query .= "INNER JOIN $cq_sou->bd_table $cq_sou_a ";
            $query .= "  ON ($cq_sou_a.$cq_sou->bd_id = $cqa.$cq->bd_id_source) ";
            $query .= "INNER JOIN $cq_tor->bd_table $cq_tor_a ";
            $query .= "  ON ($cq_tor_a.$cq_tor->bd_id = $cqa.$cq->bd_id_torneo) ";

            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = 1 ";
            
            if(!empty($diff_id)){
                $query .= "AND $cqa.$cq->bd_id <> :$cq->bd_id ";
            }
            if(!empty($fecha)){
                $query .= "AND $cqa.$cq->bd_fecha = :$cq->bd_fecha ";
            }
            if(!empty($id_equipo_home)){
                $query .= "AND $cqa.$cq->bd_id_equipo_home = :$cq->bd_id_equipo_home ";
            }
            if(!empty($id_equipo_away)){
                $query .= "AND $cqa.$cq->bd_id_equipo_away = :$cq->bd_id_equipo_away ";
            }
            if(!empty($tipo_apuesta)){
                $query .= "AND $cqa.$cq->bd_tipo_apuesta = :$cq->bd_tipo_apuesta ";
            }
            if(!empty($tipo_apuesta_add)){
                $query .= "AND $cqa.$cq->bd_tipo_apuesta_add = :$cq->bd_tipo_apuesta_add ";
            }
            if(!empty($id_apuesta_combinada)){
                $query .= "AND $cqa.$cq->bd_id_apuesta_combinada = :$cq->bd_id_apuesta_combinada ";
            } else{
                $query .= "  AND $cqa.$cq->bd_id_apuesta_combinada = 0 ";
            }
            if(!empty($id_source)){
                $query .= "AND $cqa.$cq->bd_id_source = :$cq->bd_id_source ";
            }

            $query .= "ORDER BY ";
            $query .= "   $cqa.$cq->bd_fecha_partido ASC ";
            $query .= "  ,$cqa.$cq->bd_id ";

            echo '<script>console.log("' . $query . '");</script>';

            $statement = $conexion->prepare($query);

            if(!empty($diff_id)){
                $statement->bindValue(":$cq->bd_id", ordena($diff_id));
            }
            if(!empty($fecha)){
                $statement->bindValue(":$cq->bd_fecha", $fecha);
            }
            if(!empty($id_equipo_home)){
                $statement->bindValue(":$cq->bd_id_equipo_home", ordena($id_equipo_home));
            }
            if(!empty($id_equipo_away)){
                $statement->bindValue(":$cq->bd_id_equipo_away", ordena($id_equipo_away));
            }
            if(!empty($tipo_apuesta)){
                $statement->bindValue(":$cq->bd_tipo_apuesta", $tipo_apuesta);
            }
            if(!empty($tipo_apuesta_add)){
                $statement->bindValue(":$cq->bd_tipo_apuesta_add", $tipo_apuesta_add);
            }
            if(!empty($id_apuesta_combinada)){
                $statement->bindValue(":$cq->bd_id_apuesta_combinada", ordena($id_apuesta_combinada));
            }
            if(!empty($id_source)){
                $statement->bindValue(":$cq->bd_id_source", ordena($id_source));
            }

            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado, $conexion);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function agregar(PDO $conexion): void
    {
        try {
            $this->validate_data($conexion);			
            $this->validate_data_agregar($conexion);			

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "   $cq->bd_fecha ";
            $query .= "  ,$cq->bd_id_estado ";
            $query .= "  ,$cq->bd_id_source ";
            $query .= "  ,$cq->bd_id_channel ";
            $query .= "  ,$cq->bd_stake ";
            $query .= "  ,$cq->bd_stake_source ";
            $query .= "  ,$cq->bd_id_equipo_home ";
            $query .= "  ,$cq->bd_id_equipo_away ";
            $query .= "  ,$cq->bd_id_torneo ";
            $query .= "  ,$cq->bd_fecha_partido ";
            $query .= "  ,$cq->bd_tipo_apuesta ";
            $query .= "  ,$cq->bd_tipo_apuesta_add ";
            $query .= "  ,$cq->bd_tipo_apuesta_add2 ";
            $query .= "  ,$cq->bd_apostado ";
            $query .= "  ,$cq->bd_cuota_source ";
            $query .= "  ,$cq->bd_cuota_grupo ";
            $query .= "  ,$cq->bd_cuota_real ";
            $query .= "  ,$cq->bd_bankroll ";
            $query .= "  ,$cq->bd_id_stakazo ";
            $query .= "  ,$cq->bd_probabilidades ";
            $query .= "  ,$cq->bd_usado ";
            $query .= "  ,$cq->bd_es_combinada ";
            $query .= "  ,$cq->bd_id_apuesta_combinada ";
            $query .= ") VALUES (";
            $query .= "   :$cq->bd_fecha ";
            $query .= "  ,:$cq->bd_id_estado ";
            $query .= "  ,:$cq->bd_id_source ";
            $query .= "  ,:$cq->bd_id_channel ";
            $query .= "  ,:$cq->bd_stake ";
            $query .= "  ,:$cq->bd_stake_source ";
            $query .= "  ,:$cq->bd_id_equipo_home ";
            $query .= "  ,:$cq->bd_id_equipo_away ";
            $query .= "  ,:$cq->bd_id_torneo ";
            $query .= "  ,:$cq->bd_fecha_partido ";
            $query .= "  ,:$cq->bd_tipo_apuesta ";
            $query .= "  ,:$cq->bd_tipo_apuesta_add ";
            $query .= "  ,:$cq->bd_tipo_apuesta_add2 ";
            $query .= "  ,:$cq->bd_apostado ";
            $query .= "  ,:$cq->bd_cuota_source ";
            $query .= "  ,:$cq->bd_cuota_grupo ";
            $query .= "  ,:$cq->bd_cuota_real ";
            $query .= "  ,:$cq->bd_bankroll ";
            $query .= "  ,:$cq->bd_id_stakazo ";
            $query .= "  ,:$cq->bd_probabilidades ";
            $query .= "  ,:$cq->bd_usado ";
            $query .= "  ,:$cq->bd_es_combinada ";
            $query .= "  ,:$cq->bd_id_apuesta_combinada ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_fecha", $this->fecha);
            $statement->bindValue(":$cq->bd_id_estado", BtApuestaEstado::ID_PENDIENTE);
            $statement->bindValue(":$cq->bd_id_source", ordena($this->source->id));
            $statement->bindValue(":$cq->bd_id_channel", ordena($this->channel->id));
            $statement->bindValue(":$cq->bd_stake", $this->stake);
            $statement->bindValue(":$cq->bd_stake_source", $this->stake_source);
            $statement->bindValue(":$cq->bd_id_equipo_home", ordena($this->equipo_home->id));
            $statement->bindValue(":$cq->bd_id_equipo_away", ordena($this->equipo_away->id));
            $statement->bindValue(":$cq->bd_id_torneo", ordena($this->torneo->id));
            $statement->bindValue(":$cq->bd_fecha_partido", $this->fecha_partido . ' ' . $this->fecha_partido_hora);
            $statement->bindValue(":$cq->bd_tipo_apuesta", $this->tipo_apuesta);
            $statement->bindValue(":$cq->bd_tipo_apuesta_add", $this->tipo_apuesta_add);
            $statement->bindValue(":$cq->bd_tipo_apuesta_add2", $this->tipo_apuesta_add2);
            $statement->bindValue(":$cq->bd_apostado", $this->apostado);
            $statement->bindValue(":$cq->bd_cuota_source", $this->cuota_source);
            $statement->bindValue(":$cq->bd_cuota_grupo", $this->cuota_grupo);
            $statement->bindValue(":$cq->bd_cuota_real", $this->cuota_real);
            $statement->bindValue(":$cq->bd_bankroll", $this->bankroll);
            $statement->bindValue(":$cq->bd_id_stakazo", ordena($this->stakazo->id));
            $statement->bindValue(":$cq->bd_probabilidades", $this->probabilidades);
            $statement->bindValue(":$cq->bd_usado", $this->usado);
            $statement->bindValue(":$cq->bd_es_combinada", $this->es_combinada);
            $statement->bindValue(":$cq->bd_id_apuesta_combinada", ordena($this->id_apuesta_combinada));
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modificar(PDO $conexion): void
    {
        try {
            $this->validate_data($conexion);
            $this->validate_data_modificar($conexion);

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "   $cq->bd_fecha = :$cq->bd_fecha ";
            $query .= "  ,$cq->bd_id_source = :$cq->bd_id_source ";
            $query .= "  ,$cq->bd_id_channel = :$cq->bd_id_channel ";
            $query .= "  ,$cq->bd_stake = :$cq->bd_stake ";
            $query .= "  ,$cq->bd_stake_source = :$cq->bd_stake_source ";
            $query .= "  ,$cq->bd_id_equipo_home = :$cq->bd_id_equipo_home ";
            $query .= "  ,$cq->bd_id_equipo_away = :$cq->bd_id_equipo_away ";
            $query .= "  ,$cq->bd_id_torneo = :$cq->bd_id_torneo ";
            $query .= "  ,$cq->bd_fecha_partido = :$cq->bd_fecha_partido ";
            $query .= "  ,$cq->bd_tipo_apuesta = :$cq->bd_tipo_apuesta ";
            $query .= "  ,$cq->bd_tipo_apuesta_add = :$cq->bd_tipo_apuesta_add ";
            $query .= "  ,$cq->bd_tipo_apuesta_add2 = :$cq->bd_tipo_apuesta_add2 ";
            $query .= "  ,$cq->bd_apostado = :$cq->bd_apostado ";
            $query .= "  ,$cq->bd_cuota_source = :$cq->bd_cuota_source ";
            $query .= "  ,$cq->bd_cuota_grupo = :$cq->bd_cuota_grupo ";
            $query .= "  ,$cq->bd_cuota_real = :$cq->bd_cuota_real ";
            $query .= "  ,$cq->bd_bankroll = :$cq->bd_bankroll ";
            $query .= "  ,$cq->bd_id_stakazo = :$cq->bd_id_stakazo ";
            $query .= "  ,$cq->bd_probabilidades = :$cq->bd_probabilidades ";
            $query .= "  ,$cq->bd_usado = :$cq->bd_usado ";
            $query .= "  ,$cq->bd_es_combinada = :$cq->bd_es_combinada ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_fecha", $this->fecha);
            $statement->bindValue(":$cq->bd_id_source", ordena($this->source->id));
            $statement->bindValue(":$cq->bd_id_channel", ordena($this->channel->id));
            $statement->bindValue(":$cq->bd_stake", $this->stake);
            $statement->bindValue(":$cq->bd_stake_source", $this->stake_source);
            $statement->bindValue(":$cq->bd_id_equipo_home", ordena($this->equipo_home->id));
            $statement->bindValue(":$cq->bd_id_equipo_away", ordena($this->equipo_away->id));
            $statement->bindValue(":$cq->bd_id_torneo", ordena($this->torneo->id));
            $statement->bindValue(":$cq->bd_fecha_partido", $this->fecha_partido . ' ' . $this->fecha_partido_hora);
            $statement->bindValue(":$cq->bd_tipo_apuesta", $this->tipo_apuesta);
            $statement->bindValue(":$cq->bd_tipo_apuesta_add", $this->tipo_apuesta_add);
            $statement->bindValue(":$cq->bd_tipo_apuesta_add2", $this->tipo_apuesta_add2);
            $statement->bindValue(":$cq->bd_apostado", $this->apostado);
            $statement->bindValue(":$cq->bd_cuota_source", $this->cuota_source);
            $statement->bindValue(":$cq->bd_cuota_grupo", $this->cuota_grupo);
            $statement->bindValue(":$cq->bd_cuota_real", $this->cuota_real);
            $statement->bindValue(":$cq->bd_bankroll", $this->bankroll);
            $statement->bindValue(":$cq->bd_id_stakazo", ordena($this->stakazo->id));
            $statement->bindValue(":$cq->bd_probabilidades", $this->probabilidades);
            $statement->bindValue(":$cq->bd_usado", $this->usado);
            $statement->bindValue(":$cq->bd_es_combinada", $this->es_combinada);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modificar_marcar_cerrado($ganado, PDO $conexion): void
    {
        try {
            $cq = new self;

            $this->ganado     = $ganado;
            $sel_apuesta      = self::get($this->id, $conexion);
            $this->apostado   = $sel_apuesta->apostado;
            $this->cuota_real = $sel_apuesta->cuota_real;
            $this->calcular_recibido();
            $this->calcular_pagado();

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "   $cq->bd_ganado = :$cq->bd_ganado ";
            $query .= "  ,$cq->bd_id_estado = :$cq->bd_id_estado ";
            $query .= "  ,$cq->bd_recibido = :$cq->bd_recibido ";
            $query .= "  ,$cq->bd_profit = :$cq->bd_profit ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_ganado", $this->ganado);
            $statement->bindValue(":$cq->bd_id_estado", BtApuestaEstado::ID_CERRADO);
            $statement->bindValue(":$cq->bd_recibido", $this->recibido);
            $statement->bindValue(":$cq->bd_profit", $this->profit);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modificar_marcar_publicado(PDO $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "   $cq->bd_id_estado = :$cq->bd_id_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id_estado", BtApuestaEstado::ID_PUBLICADO);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function calcular_recibido(): void
    {
        try {
            if($this->ganado == 1){
                $this->recibido = $this->apostado * $this->cuota_real;

            } else{
                $this->recibido = 0;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function calcular_pagado(): void
    {
        try {
            if($this->ganado > -1){
                $this->profit = $this->recibido - $this->apostado;

            } else{
                $this->profit = 0;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function calcular_total_profit($apuestas): int|float
    {
        try {
            $total_profit = 0;

            /** @var self[] $apuestas */
            foreach ($apuestas as $apuesta) {
                $total_profit += $apuesta->profit;    
            }

            return $total_profit;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data($conexion): void
    {
        try {
            validar_textovacio($this->fecha, 'Debe especificar la fecha.');
            validar_textovacio($this->source->nombre, 'Debe especificar el source.');
            validar_textovacio($this->equipo_home->nombre, 'Debe especificar el Home.');
            validar_textovacio($this->equipo_away->nombre, 'Debe especificar el Away.');
            validar_textovacio($this->torneo->nombre, 'Debe especificar el torneo.');

            $this->equipo_home->nombre = mb_strtoupper($this->equipo_home->nombre);
            $this->equipo_away->nombre = mb_strtoupper($this->equipo_away->nombre);
            $this->source->nombre      = mb_strtoupper($this->source->nombre);
            $this->channel->nombre     = mb_strtoupper($this->channel->nombre);
            $this->torneo->nombre      = mb_strtoupper($this->torneo->nombre);
            $this->stakazo->nombre     = mb_strtoupper($this->stakazo->nombre);
            
            $this->equipo_home->id = Equipo::get(array('nombre' => $this->equipo_home->nombre), $conexion)->id;
            $this->equipo_away->id = Equipo::get(array('nombre' => $this->equipo_away->nombre), $conexion)->id;

            if(empty($this->equipo_home->id)){
                throw new Exception("Equipo Home no encontrado.");
            }
            if(empty($this->equipo_away->id)){
                throw new Exception("Equipo Home no encontrado.");
            }
            
            $this->source->id           = Source::get(array('nombre' => $this->source->nombre), $conexion)->id;
            $this->channel->id          = Channel::get(array('nombre' => $this->channel->nombre), $conexion)->id;
            $this->torneo->id           = Torneo::get(array('nombre' => $this->torneo->nombre), $conexion)->id;
            $this->stakazo->id          = Stakazo::get(array('nombre' => $this->stakazo->nombre), $conexion)->id;
            $this->tipo_apuesta         = mb_strtoupper($this->tipo_apuesta);
            $this->tipo_apuesta_add     = mb_strtoupper($this->tipo_apuesta_add);
            $this->tipo_apuesta_add2    = mb_strtoupper($this->tipo_apuesta_add2);
            $this->id_apuesta_combinada = (empty($this->id_apuesta_combinada)) ? desordena(0) : $this->id_apuesta_combinada;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data_agregar($conexion): void
    {
        try {
            //solo hacer la validacion de apuestas repetidas si la apuesta no es combinada.
            if($this->tipo_apuesta != self::TEXTO_COMBINADA){
                $param                     = array();
                $param['fecha']            = $this->fecha;
                $param['id_equipo_home']   = $this->equipo_home->id;
                $param['id_equipo_away']   = $this->equipo_away->id;
                $param['tipo_apuesta']     = $this->tipo_apuesta;
                $param['tipo_apuesta_add'] = $this->tipo_apuesta_add;
                $param['id_source']        = $this->source->id;
                $exist_apuestas            = self::get_list($param, $conexion);

                if(count($exist_apuestas) > 0){
                    throw new Exception("Ya existe esta apuesta");
                }
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data_modificar($conexion): void
    {
        try {
            //solo hacer la validacion de apuestas repetidas si la apuesta no es combinada.
            if($this->tipo_apuesta != self::TEXTO_COMBINADA){
                $param                     = array();
                $param['diff_id']          = $this->id;
                $param['fecha']            = $this->fecha;
                $param['id_equipo_home']   = $this->equipo_home->id;
                $param['id_equipo_away']   = $this->equipo_away->id;
                $param['tipo_apuesta']     = $this->tipo_apuesta;
                $param['tipo_apuesta_add'] = $this->tipo_apuesta_add;
                $param['id_source']        = $this->source->id;
                $exist_apuestas            = self::get_list($param, $conexion);

                if(count($exist_apuestas) > 0){
                    throw new Exception("Ya existe esta apuesta");
                }
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>