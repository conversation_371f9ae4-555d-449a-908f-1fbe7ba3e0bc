<?php

require_once __ROOT__ . '/src/classes/partido.php';

class PartidoCorner
{
    public string $id;
    public string $idpartido;
    public string $home;
    public int $homecorners;
    public string $away;    
    public int $awaycorners;    
    public string $torneo;
    public string $fecha;
    public int $mejoropt1;
    public int $mejoropt2;
    private string $bd_table = 'partidos_corners';
    private string $bd_alias = 'parcor';
    private string $bd_id = 'id_partido_corner';
    private string $bd_idpartido = 'id_partido';
    private string $bd_home = 'home';
    private string $bd_homecorners = 'home_corners';
    private string $bd_away = 'away';
    private string $bd_awaycorners = 'away_corners';
    private string $bd_torneo = 'torneo';
    private string $bd_fecha = 'fecha';
    const VALUETOYELLOW = 51;
    const VALUETOGREEN = 60;
    const VALUETOGREENSTRONG = 70;
    const BGTOGREEN = 'bg-success';
    const BGTOGREENSTRONG = 'bg-green-800';
    const BGTOYELLOW = 'bg-warning';
    const DEFAULT_MASXTOTALCORNERS = 9.5;
    const DEFAULT_MENOSXTOTALCORNERS = 10.5;
    const DEFAULT_MASXCORNERSHOME = 5.5;
    const DEFAULT_MENOSXCORNERSHOME = 5.5;
    const DEFAULT_MASXCORNERSAWAY = 5.5;
    const DEFAULT_MENOSXCORNERSAWAY = 5.5;

    function __construct()
    {
        $this->id = '';
        $this->idpartido = '';
        $this->home = '';
        $this->homecorners = 0;
        $this->away = '';
        $this->awaycorners = 0;
        $this->torneo = '';
        $this->fecha = '';
        $this->mejoropt1 = 0;
        $this->mejoropt2 = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id = desordena($resultado[$cq->bd_id]);
            $objeto->idpartido = desordena($resultado[$cq->bd_idpartido]);
            $objeto->home = $resultado[$cq->bd_home];
            $objeto->homecorners = $resultado[$cq->bd_homecorners];
            $objeto->away = $resultado[$cq->bd_away];
            $objeto->awaycorners = $resultado[$cq->bd_awaycorners];
            $objeto->torneo = $resultado[$cq->bd_torneo];
            $objeto->fecha = $resultado[$cq->bd_fecha];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }    

    public static function constructArrayNumeroPartidos():array
    {
        $numeropartidos = array();
        $numeropartidos['todospartidos'] = 0;
        $numeropartidos['todoshomevsawaypartidos'] = 0;
        $numeropartidos['homeathomevsawaypartidos'] = 0;
        $numeropartidos['homeatawayvsawaypartidos'] = 0;
        $numeropartidos['homevsallpartidos'] = 0;
        $numeropartidos['homeathomevsall_partidos'] = 0;
        $numeropartidos['homeatawayvsall_partidos'] = 0;
        $numeropartidos['awayvsall_partidos'] = 0;
        $numeropartidos['awayathomevsall_partidos'] = 0;
        $numeropartidos['awayatawayvsall_partidos'] = 0;

        return $numeropartidos;
    }

    private static function constructArrayCumple():array
    {
        $cumple = array();            
        $cumple['todos_opt1'] = 0;
        $cumple['todos_opt2'] = 0;
        $cumple['todos_homevsaway_opt1'] = 0;
        $cumple['todos_homevsaway_opt2'] = 0;
        $cumple['homeathomevsaway_opt1'] = 0;
        $cumple['homeathomevsaway_opt2'] = 0;
        $cumple['homeatawayvsaway_opt1'] = 0;
        $cumple['homeatawayvsaway_opt2'] = 0;
        $cumple['homevsall_opt1'] = 0;
        $cumple['homevsall_opt2'] = 0;
        $cumple['homeathomevsall_opt1'] = 0;
        $cumple['homeathomevsall_opt2'] = 0;
        $cumple['homeatawayvsall_opt1'] = 0;
        $cumple['homeatawayvsall_opt2'] = 0;
        $cumple['awayvsall_opt1'] = 0;
        $cumple['awayvsall_opt2'] = 0;
        $cumple['awayathomevsall_opt1'] = 0;
        $cumple['awayathomevsall_opt2'] = 0;
        $cumple['awayatawayvsall_opt1'] = 0;
        $cumple['awayatawayvsall_opt2'] = 0;
        $cumple['num_cornershome_todos_homevsaway'] = 0;
        $cumple['num_cornersaway_todos_homevsaway'] = 0;
        $cumple['avg_cornershome_todos_homevsaway'] = 0;
        $cumple['avg_cornersaway_todos_homevsaway'] = 0;
        $cumple['num_cornershome_homeathomevsaway'] = 0;
        $cumple['num_cornersaway_homeathomevsaway'] = 0;
        $cumple['avg_cornershome_homeathomevsaway'] = 0;
        $cumple['avg_cornersaway_homeathomevsaway'] = 0;
        $cumple['num_cornershome_homeatawayvsaway'] = 0;
        $cumple['num_cornersaway_homeatawayvsaway'] = 0;
        $cumple['avg_cornershome_homeatawayvsaway'] = 0;
        $cumple['avg_cornersaway_homeatawayvsaway'] = 0;
        $cumple['avg_cornershome_homevsall'] = 0;
        $cumple['avg_cornersaway_homevsall'] = 0;
        $cumple['num_cornershome_homevsall'] = 0;
        $cumple['num_cornersaway_homevsall'] = 0;

        return $cumple;
    }

    public static function constructArrayPromCorner():array
    {
        $prom_corners = array();  
        $prom_corners['num_cornershome_todos_homevsaway'] = 0;
        $prom_corners['num_cornersaway_todos_homevsaway'] = 0;
        $prom_corners['avg_cornershome_todos_homevsaway'] = 0;
        $prom_corners['avg_cornersaway_todos_homevsaway'] = 0;
        $prom_corners['num_cornershome_homeathomevsaway'] = 0;
        $prom_corners['num_cornersaway_homeathomevsaway'] = 0;
        $prom_corners['avg_cornershome_homeathomevsaway'] = 0;
        $prom_corners['avg_cornersaway_homeathomevsaway'] = 0;
        $prom_corners['num_cornershome_homeatawayvsaway'] = 0;
        $prom_corners['num_cornersaway_homeatawayvsaway'] = 0;
        $prom_corners['avg_cornershome_homeatawayvsaway'] = 0;
        $prom_corners['avg_cornersaway_homeatawayvsaway'] = 0;
        $prom_corners['avg_cornershome_homevsall'] = 0;
        $prom_corners['avg_cornersaway_homevsall'] = 0;
        $prom_corners['num_cornershome_homevsall'] = 0;
        $prom_corners['num_cornersaway_homevsall'] = 0;
        $prom_corners['avg_cornershome_homeathomevsall'] = 0;
        $prom_corners['avg_cornersaway_homeathomevsall'] = 0;
        $prom_corners['num_cornershome_homeathomevsall'] = 0;
        $prom_corners['num_cornersaway_homeathomevsall'] = 0;
        $prom_corners['avg_cornershome_homeatawayvsall'] = 0;
        $prom_corners['avg_cornersaway_homeatawayvsall'] = 0;
        $prom_corners['num_cornershome_homeatawayvsall'] = 0;
        $prom_corners['num_cornersaway_homeatawayvsall'] = 0;
        $prom_corners['num_cornershome_awayvsall'] = 0;
        $prom_corners['num_cornersaway_awayvsall'] = 0;
        $prom_corners['avg_cornersaway_awayvsall'] = 0;
        $prom_corners['avg_cornershome_awayvsall'] = 0;
        $prom_corners['num_cornershome_awayathomevsall'] = 0;
        $prom_corners['num_cornersaway_awayathomevsall'] = 0;
        $prom_corners['num_cornershome_awayatawayvsall'] = 0;
        $prom_corners['num_cornersaway_awayatawayvsall'] = 0;
        $prom_corners['avg_cornersaway_awayathomevsall'] = 0;
        $prom_corners['avg_cornershome_awayathomevsall'] = 0;
        $prom_corners['avg_cornershome_awayatawayvsall'] = 0;
        $prom_corners['avg_cornersaway_awayatawayvsall'] = 0;
        $prom_corners['num_cornerstotal_todos_homevsaway'] = 0;
        $prom_corners['num_cornerstotal_homeathomevsaway'] = 0;
        $prom_corners['num_cornerstotal_homeatawayvsaway'] = 0;
        $prom_corners['num_cornerstotal_homevsall'] = 0;
        $prom_corners['num_cornerstotal_homeathomevsall'] = 0;
        $prom_corners['num_cornerstotal_homeatawayvsall'] = 0;
        $prom_corners['num_cornerstotal_awayvsall'] = 0;
        $prom_corners['num_cornerstotal_awayathomevsall'] = 0;
        $prom_corners['num_cornerstotal_awayatawayvsall'] = 0;
        $prom_corners['avg_cornerstotal_todos_homevsaway'] = 0;
        $prom_corners['avg_cornerstotal_homeathomevsaway'] = 0;
        $prom_corners['avg_cornerstotal_homeatawayvsaway'] = 0;
        $prom_corners['avg_cornerstotal_homevsall'] = 0;
        $prom_corners['avg_cornerstotal_homeathomevsall'] = 0;
        $prom_corners['avg_cornerstotal_homeatawayvsall'] = 0;
        $prom_corners['avg_cornerstotal_awayvsall'] = 0;
        $prom_corners['avg_cornerstotal_awayathomevsall'] = 0;
        $prom_corners['avg_cornerstotal_awayatawayvsall'] = 0;

        return $prom_corners;
    }
    public static function constructArrayPromCornerCumple():array
    {
        $promcorners_cumple = array();  
        $promcorners_cumple['masxtotalcorners_cornerstotal_todos_homevsaway'] = 0;
        $promcorners_cumple['menosxtotalcorners_cornerstotal_todos_homevsaway'] = 0;
        $promcorners_cumple['masxtotalcorners_cornerstotal_homeathomevsaway'] = 0;
        $promcorners_cumple['menosxtotalcorners_cornerstotal_homeathomevsaway'] = 0;
        $promcorners_cumple['masxtotalcorners_cornerstotal_homeatawayvsaway'] = 0;
        $promcorners_cumple['menosxtotalcorners_cornerstotal_homeatawayvsaway'] = 0;
        $promcorners_cumple['masxtotalcorners_cornerstotal_homevsall'] = 0;
        $promcorners_cumple['menosxtotalcorners_cornerstotal_homevsall'] = 0;
        $promcorners_cumple['masxtotalcorners_cornerstotal_homeathomevsall'] = 0;
        $promcorners_cumple['menosxtotalcorners_cornerstotal_homeathomevsall'] = 0;
        $promcorners_cumple['masxtotalcorners_cornerstotal_homeatawayvsall'] = 0;
        $promcorners_cumple['menosxtotalcorners_cornerstotal_homeatawayvsall'] = 0;
        $promcorners_cumple['masxtotalcorners_cornerstotal_awayvsall'] = 0;
        $promcorners_cumple['menosxtotalcorners_cornerstotal_awayvsall'] = 0;
        $promcorners_cumple['masxtotalcorners_cornerstotal_awayathomevsall'] = 0;
        $promcorners_cumple['menosxtotalcorners_cornerstotal_awayathomevsall'] = 0;
        $promcorners_cumple['masxtotalcorners_cornerstotal_awayatawayvsall'] = 0;
        $promcorners_cumple['menosxtotalcorners_cornerstotal_awayatawayvsall'] = 0;

        $promcorners_cumple['masxcornershome_cornerstotal_todos_homevsaway'] = 0;
        $promcorners_cumple['menosxcornershome_cornerstotal_todos_homevsaway'] = 0;
        $promcorners_cumple['masxcornershome_cornerstotal_homeathomevsaway'] = 0;
        $promcorners_cumple['menosxcornershome_cornerstotal_homeathomevsaway'] = 0;
        $promcorners_cumple['masxcornershome_cornerstotal_homeatawayvsaway'] = 0;
        $promcorners_cumple['menosxcornershome_cornerstotal_homeatawayvsaway'] = 0;
        $promcorners_cumple['masxcornershome_cornerstotal_homeathomevsall'] = 0;
        $promcorners_cumple['menosxcornershome_cornerstotal_homeathomevsall'] = 0;
        $promcorners_cumple['masxcornershome_cornerstotal_homeatawayvsall'] = 0;
        $promcorners_cumple['menosxcornershome_cornerstotal_homeatawayvsall'] = 0;

        $promcorners_cumple['masxcornersaway_cornerstotal_todos_homevsaway'] = 0;
        $promcorners_cumple['menosxcornersaway_cornerstotal_todos_homevsaway'] = 0;
        $promcorners_cumple['masxcornersaway_cornerstotal_homeathomevsaway'] = 0;
        $promcorners_cumple['menosxcornersaway_cornerstotal_homeathomevsaway'] = 0;
        $promcorners_cumple['masxcornersaway_cornerstotal_homeatawayvsaway'] = 0;
        $promcorners_cumple['menosxcornersaway_cornerstotal_homeatawayvsaway'] = 0;
        $promcorners_cumple['masxcornersaway_cornerstotal_awayathomevsall'] = 0;
        $promcorners_cumple['menosxcornersaway_cornerstotal_awayathomevsall'] = 0;
        $promcorners_cumple['masxcornersaway_cornerstotal_awayatawayvsall'] = 0;
        $promcorners_cumple['menosxcornersaway_cornerstotal_awayatawayvsall'] = 0;

        return $promcorners_cumple;
    }

    public static function constructArrayProbabilidades():array
    {
        $probabilidades = array();        
        $probabilidades['cumple_todos_homevsaway_opt1'] = 0;
        $probabilidades['porc_cumple_todos_homevsaway_opt1'] = 0;
        $probabilidades['color_cumple_todos_homevsaway_opt1'] = '';
        $probabilidades['mejor_cumple_todos_homevsaway_opt1'] = 0;
        $probabilidades['cumple_todos_homevsaway_opt2'] = 0;
        $probabilidades['porc_cumple_todos_homevsaway_opt2'] = 0;
        $probabilidades['color_cumple_todos_homevsaway_opt2'] = '';
        $probabilidades['mejor_cumple_todos_homevsaway_opt2'] = 0;
        $probabilidades['cumple_homeathomevsaway_opt1'] = 0;
        $probabilidades['porc_cumple_homeathomevsaway_opt1'] = 0;
        $probabilidades['color_cumple_homeathomevsaway_opt1'] = '';
        $probabilidades['mejor_cumple_homeathomevsaway_opt1'] = 0;
        $probabilidades['cumple_homeathomevsaway_opt2'] = 0;
        $probabilidades['porc_cumple_homeathomevsaway_opt2'] = 0;
        $probabilidades['color_cumple_homeathomevsaway_opt2'] = '';
        $probabilidades['mejor_cumple_homeathomevsaway_opt2'] = 0;
        $probabilidades['cumple_homeatawayvsaway_opt1'] = 0;
        $probabilidades['porc_cumple_homeatawayvsaway_opt1'] = 0;
        $probabilidades['color_cumple_homeatawayvsaway_opt1'] = '';
        $probabilidades['mejor_cumple_homeatawayvsaway_opt1'] = 0;        
        $probabilidades['cumple_homeatawayvsaway_opt2'] = 0;
        $probabilidades['porc_cumple_homeatawayvsaway_opt2'] = 0;
        $probabilidades['color_cumple_homeatawayvsaway_opt2'] = '';
        $probabilidades['mejor_cumple_homeatawayvsaway_opt2'] = 0;
        $probabilidades['cumple_homevsall_opt1'] = 0;
        $probabilidades['porc_cumple_homevsall_opt1'] = 0;
        $probabilidades['color_cumple_homevsall_opt1'] = '';
        $probabilidades['mejor_cumple_homevsall_opt1'] = 0;
        $probabilidades['cumple_homevsall_opt2'] = 0;
        $probabilidades['porc_cumple_homevsall_opt2'] = 0;
        $probabilidades['color_cumple_homevsall_opt2'] = '';
        $probabilidades['mejor_cumple_homevsall_opt2'] = 0;
        $probabilidades['cumple_homeathomevsall_opt1'] = 0;
        $probabilidades['porc_cumple_homeathomevsall_opt1'] = 0;
        $probabilidades['color_cumple_homeathomevsall_opt1'] = '';
        $probabilidades['mejor_cumple_homeathomevsall_opt1'] = 0;
        $probabilidades['cumple_homeathomevsall_opt2'] = 0;
        $probabilidades['porc_cumple_homeathomevsall_opt2'] = 0;
        $probabilidades['color_cumple_homeathomevsall_opt2'] = '';
        $probabilidades['mejor_cumple_homeathomevsall_opt2'] = 0;
        $probabilidades['cumple_homeatawayvsall_opt1'] = 0;
        $probabilidades['porc_cumple_homeatawayvsall_opt1'] = 0;
        $probabilidades['color_cumple_homeatawayvsall_opt1'] = '';
        $probabilidades['mejor_cumple_homeatawayvsall_opt1'] = 0;
        $probabilidades['cumple_homeatawayvsall_opt2'] = 0;
        $probabilidades['porc_cumple_homeatawayvsall_opt2'] = 0;
        $probabilidades['color_cumple_homeatawayvsall_opt2'] = '';
        $probabilidades['mejor_cumple_homeatawayvsall_opt2'] = 0;
        $probabilidades['cumple_awayvsall_opt1'] = 0;
        $probabilidades['porc_cumple_awayvsall_opt1'] = 0;
        $probabilidades['color_cumple_awayvsall_opt1'] = '';
        $probabilidades['mejor_cumple_awayvsall_opt1'] = 0;
        $probabilidades['cumple_awayvsall_opt2'] = 0;
        $probabilidades['porc_cumple_awayvsall_opt2'] = 0;
        $probabilidades['color_cumple_awayvsall_opt2'] = '';
        $probabilidades['mejor_cumple_awayvsall_opt2'] = 0;
        $probabilidades['cumple_awayathomevsall_opt1'] = 0;
        $probabilidades['porc_cumple_awayathomevsall_opt1'] = 0;
        $probabilidades['color_cumple_awayathomevsall_opt1'] = '';
        $probabilidades['mejor_cumple_awayathomevsall_opt1'] = 0;
        $probabilidades['cumple_awayathomevsall_opt2'] = 0;
        $probabilidades['porc_cumple_awayathomevsall_opt2'] = 0;
        $probabilidades['color_cumple_awayathomevsall_opt2'] = '';
        $probabilidades['mejor_cumple_awayathomevsall_opt2'] = 0;
        $probabilidades['cumple_awayatawayvsall_opt1'] = 0;
        $probabilidades['porc_cumple_awayatawayvsall_opt1'] = 0;
        $probabilidades['color_cumple_awayatawayvsall_opt1'] = '';
        $probabilidades['mejor_cumple_awayatawayvsall_opt1'] = 0;
        $probabilidades['cumple_awayatawayvsall_opt2'] = 0;
        $probabilidades['porc_cumple_awayatawayvsall_opt2'] = 0;
        $probabilidades['color_cumple_awayatawayvsall_opt2'] = '';
        $probabilidades['mejor_cumple_awayatawayvsall_opt2'] = 0;
        $probabilidades['total_mejoropt1'] = 0;
        $probabilidades['total_mejoropt2'] = 0;

        return $probabilidades;
    }

    public static function constructArrayParaApuesta($opt, $omitirseccion, $probabilidades):array
    {
        try {   
            $probabilidadesapuesta = array();                     
            $probabilidadesapuesta['cumple_todos_homevsaway'] = $probabilidades["cumple_todos_homevsaway_opt$opt"];
            $probabilidadesapuesta['porc_cumple_todos_homevsaway'] = $probabilidades["porc_cumple_todos_homevsaway_opt$opt"];
            $probabilidadesapuesta['color_cumple_todos_homevsaway'] = $probabilidades["color_cumple_todos_homevsaway_opt$opt"];
            $probabilidadesapuesta['cumple_homeathomevsaway'] = $probabilidades["cumple_homeathomevsaway_opt$opt"];
            $probabilidadesapuesta['porc_cumple_homeathomevsaway'] = $probabilidades["porc_cumple_homeathomevsaway_opt$opt"];
            $probabilidadesapuesta['color_cumple_homeathomevsaway'] = $probabilidades["color_cumple_homeathomevsaway_opt$opt"];
            $probabilidadesapuesta['cumple_homeatawayvsaway'] = $probabilidades["cumple_homeatawayvsaway_opt$opt"];
            $probabilidadesapuesta['porc_cumple_homeatawayvsaway'] = $probabilidades["porc_cumple_homeatawayvsaway_opt$opt"];
            $probabilidadesapuesta['color_cumple_homeatawayvsaway'] = $probabilidades["color_cumple_homeatawayvsaway_opt$opt"];
            
            if($omitirseccion == "HOME"){
                $probabilidadesapuesta['cumple_homevsall'] = -1;
                $probabilidadesapuesta['porc_cumple_homevsall'] = -1;
                $probabilidadesapuesta['color_cumple_homevsall'] = -1;
                $probabilidadesapuesta['cumple_homeathomevsall'] = -1;
                $probabilidadesapuesta['porc_cumple_homeathomevsall'] = -1;
                $probabilidadesapuesta['color_cumple_homeathomevsall'] = -1;
                $probabilidadesapuesta['cumple_homeatawayvsall'] = -1;
                $probabilidadesapuesta['porc_cumple_homeatawayvsall'] = -1;
                $probabilidadesapuesta['color_cumple_homeatawayvsall'] = -1;
            } else{
                $probabilidadesapuesta['cumple_homevsall'] = $probabilidades["cumple_homevsall_opt$opt"];
                $probabilidadesapuesta['porc_cumple_homevsall'] = $probabilidades["porc_cumple_homevsall_opt$opt"];
                $probabilidadesapuesta['color_cumple_homevsall'] = $probabilidades["color_cumple_homevsall_opt$opt"];
                $probabilidadesapuesta['cumple_homeathomevsall'] = $probabilidades["cumple_homeathomevsall_opt$opt"];
                $probabilidadesapuesta['porc_cumple_homeathomevsall'] = $probabilidades["porc_cumple_homeathomevsall_opt$opt"];
                $probabilidadesapuesta['color_cumple_homeathomevsall'] = $probabilidades["color_cumple_homeathomevsall_opt$opt"];
                $probabilidadesapuesta['cumple_homeatawayvsall'] = $probabilidades["cumple_homeatawayvsall_opt$opt"];
                $probabilidadesapuesta['porc_cumple_homeatawayvsall'] = $probabilidades["porc_cumple_homeatawayvsall_opt$opt"];
                $probabilidadesapuesta['color_cumple_homeatawayvsall'] = $probabilidades["color_cumple_homeatawayvsall_opt$opt"];
            }
            
            if($omitirseccion == 'AWAY'){
                $probabilidadesapuesta['cumple_awayvsall'] = -1;
                $probabilidadesapuesta['porc_cumple_awayvsall'] = -1;
                $probabilidadesapuesta['color_cumple_awayvsall'] = -1;
                $probabilidadesapuesta['cumple_awayathomevsall'] = -1;
                $probabilidadesapuesta['porc_cumple_awayathomevsall'] = -1;
                $probabilidadesapuesta['color_cumple_awayathomevsall'] = -1;
                $probabilidadesapuesta['cumple_awayatawayvsall'] = -1;
                $probabilidadesapuesta['porc_cumple_awayatawayvsall'] = -1;
                $probabilidadesapuesta['color_cumple_awayatawayvsall'] = -1;
            } else{
                $probabilidadesapuesta['cumple_awayvsall'] = $probabilidades["cumple_awayvsall_opt$opt"];
                $probabilidadesapuesta['porc_cumple_awayvsall'] = $probabilidades["porc_cumple_awayvsall_opt$opt"];
                $probabilidadesapuesta['color_cumple_awayvsall'] = $probabilidades["color_cumple_awayvsall_opt$opt"];
                $probabilidadesapuesta['cumple_awayathomevsall'] = $probabilidades["cumple_awayathomevsall_opt$opt"];
                $probabilidadesapuesta['porc_cumple_awayathomevsall'] = $probabilidades["porc_cumple_awayathomevsall_opt$opt"];
                $probabilidadesapuesta['color_cumple_awayathomevsall'] = $probabilidades["color_cumple_awayathomevsall_opt$opt"];
                $probabilidadesapuesta['cumple_awayatawayvsall'] = $probabilidades["cumple_awayatawayvsall_opt$opt"];
                $probabilidadesapuesta['porc_cumple_awayatawayvsall'] = $probabilidades["porc_cumple_awayatawayvsall_opt$opt"];
                $probabilidadesapuesta['color_cumple_awayatawayvsall'] = $probabilidades["color_cumple_awayatawayvsall_opt$opt"];
            }
            
            return $probabilidadesapuesta;
        
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getNumCorners($idpartido, $conexion): int
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  COUNT($cqa.$cq->bd_id) ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_idpartido = :$cq->bd_idpartido ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idpartido", ordena($idpartido));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return $resultado[0];

            } else {
                return 0;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getList($idpartido, $conexion): array
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_idpartido = :$cq->bd_idpartido ";
            $query .= "GROUP BY ";
            $query .= "  $cqa.$cq->bd_idpartido ";
            $query .= "  ,$cqa.$cq->bd_fecha ";
            $query .= "  ,$cqa.$cq->bd_torneo ";
            $query .= "  ,$cqa.$cq->bd_home ";
            $query .= "  ,$cqa.$cq->bd_homecorners ";
            $query .= "  ,$cqa.$cq->bd_away ";
            $query .= "  ,$cqa.$cq->bd_awaycorners ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idpartido", ordena($idpartido));
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add($conexion): void
    {
        try {
            $this->validateData();			

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_idpartido ";
            $query .= "  ,$cq->bd_home ";
            $query .= "  ,$cq->bd_homecorners ";
            $query .= "  ,$cq->bd_away ";
            $query .= "  ,$cq->bd_awaycorners ";
            $query .= "  ,$cq->bd_torneo ";
            $query .= "  ,$cq->bd_fecha ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_idpartido ";
            $query .= "  ,:$cq->bd_home ";
            $query .= "  ,:$cq->bd_homecorners ";
            $query .= "  ,:$cq->bd_away ";
            $query .= "  ,:$cq->bd_awaycorners ";
            $query .= "  ,:$cq->bd_torneo ";
            $query .= "  ,:$cq->bd_fecha ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idpartido", ordena($this->idpartido));
            $statement->bindValue(":$cq->bd_home", $this->home);
            $statement->bindValue(":$cq->bd_homecorners", $this->homecorners);
            $statement->bindValue(":$cq->bd_away", $this->away);
            $statement->bindValue(":$cq->bd_awaycorners", $this->awaycorners);
            $statement->bindValue(":$cq->bd_torneo", $this->torneo);
            $statement->bindValue(":$cq->bd_fecha", $this->fecha);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modify($conexion): void
    {
        try {
            $this->validateData();

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_idpartido = :$cq->bd_idpartido ";
            $query .= "  ,$cq->bd_home = :$cq->bd_home ";
            $query .= "  ,$cq->bd_homecorners = :$cq->bd_homecorners ";
            $query .= "  ,$cq->bd_away = :$cq->bd_away ";
            $query .= "  ,$cq->bd_awaycorners = :$cq->bd_awaycorners ";
            $query .= "  ,$cq->bd_torneo = :$cq->bd_torneo ";
            $query .= "  ,$cq->bd_fecha = :$cq->bd_fecha ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idpartido", ordena($this->idpartido));
            $statement->bindValue(":$cq->bd_home", $this->home);
            $statement->bindValue(":$cq->bd_homecorners", $this->homecorners);
            $statement->bindValue(":$cq->bd_away", $this->away);
            $statement->bindValue(":$cq->bd_awaycorners", $this->awaycorners);
            $statement->bindValue(":$cq->bd_torneo", $this->torneo);
            $statement->bindValue(":$cq->bd_fecha", $this->fecha);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public static function calcularProbabilidades($param, $conexion){
        try {
            $idpartido = $param['idpartido'];
            $numeropartidos = $param['numeropartidos'];
            $masxtotalcorners = $param['masxtotalcorners'];
            $menosxtotalcorners = $param['menosxtotalcorners'];
            $masxcornershome = $param['masxcornershome'];
            $menosxcornershome = $param['menosxcornershome'];
            $masxcornersaway = $param['masxcornersaway'];
            $menosxcornersaway = $param['menosxcornersaway'];

            $partido = Partido::get($idpartido, $conexion);
            $partidoscorners = self::getList($idpartido, $conexion);

            $cumple_totalcorners = self::constructArrayCumple();
            $cumple_mascorners = self::constructArrayCumple();
            $cumple_cornershome = self::constructArrayCumple();
            $cumple_cornersaway = self::constructArrayCumple();
            $prom_corners = self::constructArrayPromCorner();

            /** @var self[] $partidoscorners */
            foreach ($partidoscorners as $partidocorner) {                
                //calculo para partidos same fixture
                if(($partidocorner->home == $partido->home && $partidocorner->away == $partido->away) || ($partidocorner->away == $partido->home && $partidocorner->home == $partido->away)){
                    //calculo para partidos same fixture - totalcorners
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) > $masxtotalcorners){
                        $cumple_totalcorners['todos_homevsaway_opt1']++;
                    }
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) < $menosxtotalcorners){
                        $cumple_totalcorners['todos_homevsaway_opt2']++;
                    }

                    //calculo para partidos same fixture - mascorners
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['todos_homevsaway_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['todos_homevsaway_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['todos_homevsaway_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['todos_homevsaway_opt2']++;
                    }

                    //calculo para partidos same fixture - cornershome
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $masxcornershome){
                        $cumple_cornershome['todos_homevsaway_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners > $masxcornershome){
                        $cumple_cornershome['todos_homevsaway_opt1']++;
                    }
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners < $menosxcornershome){
                        $cumple_cornershome['todos_homevsaway_opt2']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners < $menosxcornershome){
                        $cumple_cornershome['todos_homevsaway_opt2']++;
                    }

                    //calculo para partidos same fixture - cornersaway
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $masxcornersaway){
                        $cumple_cornersaway['todos_homevsaway_opt1']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners > $masxcornersaway){
                        $cumple_cornersaway['todos_homevsaway_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners < $menosxcornersaway){
                        $cumple_cornersaway['todos_homevsaway_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners < $menosxcornersaway){
                        $cumple_cornersaway['todos_homevsaway_opt2']++;
                    }

                    //calculo promedio corners | same fixture
                    $prom_corners['num_cornershome_todos_homevsaway'] += $partidocorner->homecorners;
                    $prom_corners['num_cornersaway_todos_homevsaway'] += $partidocorner->awaycorners;
                    $prom_corners['num_cornerstotal_todos_homevsaway'] += $partidocorner->homecorners + $partidocorner->awaycorners;
                }

                //calculo para partidos | home @home vs away
                if($partidocorner->home == $partido->home && $partidocorner->away == $partido->away){
                    //calculo para partidos | home @home vs away | totalcorners
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) > $masxtotalcorners){
                        $cumple_totalcorners['homeathomevsaway_opt1']++;
                    }
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) < $menosxtotalcorners){
                        $cumple_totalcorners['homeathomevsaway_opt2']++;
                    }

                    //calculo para partidos | home @home vs away | mascorners
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homeathomevsaway_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homeathomevsaway_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homeathomevsaway_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homeathomevsaway_opt2']++;
                    }

                    //calculo para partidos | home @home vs away | cornershome
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $masxcornershome){
                        $cumple_cornershome['homeathomevsaway_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners > $masxcornershome){
                        $cumple_cornershome['homeathomevsaway_opt1']++;
                    }
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners < $menosxcornershome){
                        $cumple_cornershome['homeathomevsaway_opt2']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners < $menosxcornershome){
                        $cumple_cornershome['homeathomevsaway_opt2']++;
                    }


                    //calculo para partidos | home @home vs away | cornersaway
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $masxcornersaway){
                        $cumple_cornersaway['homeathomevsaway_opt1']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners > $masxcornersaway){
                        $cumple_cornersaway['homeathomevsaway_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners < $menosxcornersaway){
                        $cumple_cornersaway['homeathomevsaway_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners < $menosxcornersaway){
                        $cumple_cornersaway['homeathomevsaway_opt2']++;
                    }

                    //calculo promedio corners | same fixture
                    $prom_corners['num_cornershome_homeathomevsaway'] += $partidocorner->homecorners;
                    $prom_corners['num_cornersaway_homeathomevsaway'] += $partidocorner->awaycorners;
                    $prom_corners['num_cornerstotal_homeathomevsaway'] += $partidocorner->homecorners + $partidocorner->awaycorners;
                }

                //calculo para partidos | home @away vs away
                if($partidocorner->home == $partido->away && $partidocorner->away == $partido->home){
                    //calculo para partidos | home @away vs away | totalcorners
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) > $masxtotalcorners){
                        $cumple_totalcorners['homeatawayvsaway_opt1']++;
                    }
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) < $menosxtotalcorners){
                        $cumple_totalcorners['homeatawayvsaway_opt2']++;
                    }

                    //calculo para partidos | home @away vs away | mascorners
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homeatawayvsaway_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homeatawayvsaway_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homeatawayvsaway_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homeatawayvsaway_opt2']++;
                    }

                    //calculo para partidos | home @away vs away | cornershome
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $masxcornershome){
                        $cumple_cornershome['homeatawayvsaway_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners > $masxcornershome){
                        $cumple_cornershome['homeatawayvsaway_opt1']++;
                    }
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners < $menosxcornershome){
                        $cumple_cornershome['homeatawayvsaway_opt2']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners < $menosxcornershome){
                        $cumple_cornershome['homeatawayvsaway_opt2']++;
                    }

                    //calculo para partidos | home @away vs away | cornersaway
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $masxcornersaway){
                        $cumple_cornersaway['homeatawayvsaway_opt1']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners > $masxcornersaway){
                        $cumple_cornersaway['homeatawayvsaway_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners < $menosxcornersaway){
                        $cumple_cornersaway['homeatawayvsaway_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners < $menosxcornersaway){
                        $cumple_cornersaway['homeatawayvsaway_opt2']++;
                    }

                    //calculo promedio corners | same fixture
                    $prom_corners['num_cornershome_homeatawayvsaway'] += $partidocorner->homecorners;
                    $prom_corners['num_cornersaway_homeatawayvsaway'] += $partidocorner->awaycorners;
                    $prom_corners['num_cornerstotal_homeatawayvsaway'] += $partidocorner->homecorners + $partidocorner->awaycorners;
                }

                //calculo para partidos home vs all
                if($partidocorner->home == $partido->home || $partidocorner->away == $partido->home){
                    //calculo para partidos home vs all - totalcorners
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) > $masxtotalcorners){
                        $cumple_totalcorners['homevsall_opt1']++;
                    }
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) < $menosxtotalcorners){
                        $cumple_totalcorners['homevsall_opt2']++;
                    }

                    //calculo para partidos home vs all - mascorners
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homevsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homevsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homevsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homevsall_opt2']++;
                    }

                    //calculo para partidos | home vs all - cornershome
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $masxcornershome){
                        $cumple_cornershome['homevsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners > $masxcornershome){
                        $cumple_cornershome['homevsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners < $menosxcornershome){
                        $cumple_cornershome['homevsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners < $menosxcornershome){
                        $cumple_cornershome['homevsall_opt2']++;
                    }

                    //calculo promedio corners | home vs all
                    $prom_corners['num_cornershome_homevsall'] += $partidocorner->homecorners;
                    $prom_corners['num_cornersaway_homevsall'] += $partidocorner->awaycorners;
                    $prom_corners['num_cornerstotal_homevsall'] += $partidocorner->homecorners + $partidocorner->awaycorners;
                }

                //calculo para partidos home @home vs all
                if($partidocorner->home == $partido->home){
                    //calculo para partidos home @home vs all - totalcorners
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) > $masxtotalcorners){
                        $cumple_totalcorners['homeathomevsall_opt1']++;
                    }
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) < $menosxtotalcorners){
                        $cumple_totalcorners['homeathomevsall_opt2']++;
                    }

                    //calculo para partidos home @home vs all - mascorners
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homeathomevsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homeathomevsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homeathomevsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homeathomevsall_opt2']++;
                    }

                    //calculo para partidos home @home vs all - cornershome
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $masxcornershome){
                        $cumple_cornershome['homeathomevsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners > $masxcornershome){
                        $cumple_cornershome['homeathomevsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners < $menosxcornershome){
                        $cumple_cornershome['homeathomevsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners < $menosxcornershome){
                        $cumple_cornershome['homeathomevsall_opt2']++;
                    }

                    //calculo promedio corners | home vs all
                    $prom_corners['num_cornershome_homeathomevsall'] += $partidocorner->homecorners;
                    $prom_corners['num_cornersaway_homeathomevsall'] += $partidocorner->awaycorners;
                    $prom_corners['num_cornerstotal_homeathomevsall'] += $partidocorner->homecorners + $partidocorner->awaycorners;
                }

                //calculo para partidos home @away vs all
                if($partidocorner->away == $partido->home){
                    //calculo para partidos home @away vs all - totalcorners
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) > $masxtotalcorners){
                        $cumple_totalcorners['homeatawayvsall_opt1']++;
                    }
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) < $menosxtotalcorners){
                        $cumple_totalcorners['homeatawayvsall_opt2']++;
                    }

                    //calculo para partidos home @away vs all - mascorners
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homeatawayvsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homeatawayvsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['homeatawayvsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['homeatawayvsall_opt2']++;
                    }

                    //calculo para partidos | home @away vs all | cornershome
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $masxcornershome){
                        $cumple_cornershome['homeatawayvsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners > $masxcornershome){
                        $cumple_cornershome['homeatawayvsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners < $menosxcornershome){
                        $cumple_cornershome['homeatawayvsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->awaycorners < $menosxcornershome){
                        $cumple_cornershome['homeatawayvsall_opt2']++;
                    }

                    //calculo promedio corners | home @away vs all
                    $prom_corners['num_cornershome_homeatawayvsall'] += $partidocorner->homecorners;
                    $prom_corners['num_cornersaway_homeatawayvsall'] += $partidocorner->awaycorners;
                    $prom_corners['num_cornerstotal_homeatawayvsall'] += $partidocorner->homecorners + $partidocorner->awaycorners;
                }

                //calculo para partidos away vs all
                if($partidocorner->home == $partido->away || $partidocorner->away == $partido->away){
                    //calculo para partidos away vs all - totalcorners
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) > $masxtotalcorners){
                        $cumple_totalcorners['awayvsall_opt1']++;
                    }
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) < $menosxtotalcorners){
                        $cumple_totalcorners['awayvsall_opt2']++;
                    }

                    //calculo para partidos away vs all - mascorners
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['awayvsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['awayvsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['awayvsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['awayvsall_opt2']++;
                    }

                    //calculo para | partidos away vs all - cornersaway
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $masxcornersaway){
                        $cumple_cornersaway['awayvsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners > $masxcornersaway){
                        $cumple_cornersaway['awayvsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners < $menosxcornersaway){
                        $cumple_cornersaway['awayvsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners < $menosxcornersaway){
                        $cumple_cornersaway['awayvsall_opt2']++;
                    }

                    //calculo promedio corners | partidos away vs all 
                    $prom_corners['num_cornershome_awayvsall'] += $partidocorner->homecorners;
                    $prom_corners['num_cornersaway_awayvsall'] += $partidocorner->awaycorners;
                    $prom_corners['num_cornerstotal_awayvsall'] += $partidocorner->homecorners + $partidocorner->awaycorners;
                }

                //calculo para partidos away @home vs all
                if($partidocorner->home == $partido->away){
                    //calculo para partidos away @home vs all - totalcorners
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) > $masxtotalcorners){
                        $cumple_totalcorners['awayathomevsall_opt1']++;
                    }
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) < $menosxtotalcorners){
                        $cumple_totalcorners['awayathomevsall_opt2']++;
                    }

                    //calculo para partidos away @home vs all - mascorners
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['awayathomevsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['awayathomevsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['awayathomevsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['awayathomevsall_opt2']++;
                    }

                    //calculo para | partidos away @home vs all - cornersaway
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $masxcornersaway){
                        $cumple_cornersaway['awayathomevsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners > $masxcornersaway){
                        $cumple_cornersaway['awayathomevsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners < $menosxcornersaway){
                        $cumple_cornersaway['awayathomevsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners < $menosxcornersaway){
                        $cumple_cornersaway['awayathomevsall_opt2']++;
                    }

                    //calculo promedio corners | partidos away @home vs all
                    $prom_corners['num_cornershome_awayathomevsall'] += $partidocorner->homecorners;
                    $prom_corners['num_cornersaway_awayathomevsall'] += $partidocorner->awaycorners;
                    $prom_corners['num_cornerstotal_awayathomevsall'] += $partidocorner->homecorners + $partidocorner->awaycorners;
                }

                //calculo para partidos away @away vs all
                if($partidocorner->away == $partido->away){
                    //calculo para partidos away @away vs all - totalcorners
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) > $masxtotalcorners){
                        $cumple_totalcorners['awayatawayvsall_opt1']++;
                    }
                    if(($partidocorner->homecorners + $partidocorner->awaycorners) < $menosxtotalcorners){
                        $cumple_totalcorners['awayatawayvsall_opt2']++;
                    }

                    //calculo para partidos away @away vs all - mascorners
                    if($partidocorner->home == $partido->home && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['awayatawayvsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->home && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['awayatawayvsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $partidocorner->awaycorners){
                        $cumple_mascorners['awayatawayvsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->homecorners < $partidocorner->awaycorners){
                        $cumple_mascorners['awayatawayvsall_opt2']++;
                    }

                    //calculo para | partidos away @away vs all | cornersaway
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners > $masxcornersaway){
                        $cumple_cornersaway['awayatawayvsall_opt1']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners > $masxcornersaway){
                        $cumple_cornersaway['awayatawayvsall_opt1']++;
                    }
                    if($partidocorner->home == $partido->away && $partidocorner->homecorners < $menosxcornersaway){
                        $cumple_cornersaway['awayatawayvsall_opt2']++;
                    }
                    if($partidocorner->away == $partido->away && $partidocorner->awaycorners < $menosxcornersaway){
                        $cumple_cornersaway['awayatawayvsall_opt2']++;
                    }

                    //calculo promedio corners | partidos away @away vs all
                    $prom_corners['num_cornershome_awayatawayvsall'] += $partidocorner->homecorners;
                    $prom_corners['num_cornersaway_awayatawayvsall'] += $partidocorner->awaycorners;
                    $prom_corners['num_cornerstotal_awayatawayvsall'] += $partidocorner->homecorners + $partidocorner->awaycorners;
                }
            }

            //probabilidades
            $probabilidades = array();

            $param = array();
            $param['numeropartidos'] = $numeropartidos; 
            $param['prom_corners'] = $prom_corners;
            $param['masxtotalcorners'] = $masxtotalcorners;
            $param['menosxtotalcorners'] = $menosxtotalcorners;
            $param['masxcornershome'] = $masxcornershome;
            $param['menosxcornershome'] = $menosxcornershome;
            $param['masxcornersaway'] = $masxcornersaway;
            $param['menosxcornersaway'] = $menosxcornersaway;
            $param['cumple'] = $cumple_totalcorners; 
            $probabilidades['totalcorners'] = self::definirProbabilidades($param);

            $param['cumple'] = $cumple_mascorners; 
            $probabilidades['mascorners'] = self::definirProbabilidades($param);

            $param['cumple'] = $cumple_cornershome; 
            $probabilidades['cornershome'] = self::definirProbabilidades($param);

            $param['cumple'] = $cumple_cornersaway; 
            $probabilidades['cornersaway'] = self::definirProbabilidades($param);

            $probabilidades['prom_corners'] = $prom_corners;

            return $probabilidades;
        
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public static function calcularNumeroPartidos($param, $conexion):array
    {
        try {
            $idpartido = $param['idpartido'];

            $partido = Partido::get($idpartido, $conexion);
            $partidoscorners = self::getList($idpartido, $conexion);
            
            $numeropartidos = self::constructArrayNumeroPartidos();
            
            /** @var self[] $partidoscorners */
            foreach ($partidoscorners as $partidocorner) {
                //calculo para todos los partidos
                $numeropartidos['todospartidos']++;

                //calculo para partidos same fixture
                if(($partidocorner->home == $partido->home && $partidocorner->away == $partido->away) || ($partidocorner->away == $partido->home && $partidocorner->home == $partido->away)){
                    $numeropartidos['todoshomevsawaypartidos']++;
                }

                //calculo para partidos home @home vs away
                if($partidocorner->home == $partido->home && $partidocorner->away == $partido->away){
                    $numeropartidos['homeathomevsawaypartidos']++;
                }

                //calculo para partidos home @away vs away
                if($partidocorner->home == $partido->away && $partidocorner->away == $partido->home){
                    $numeropartidos['homeatawayvsawaypartidos']++;
                }

                //calculo para partidos home vs all
                if($partidocorner->home == $partido->home || $partidocorner->away == $partido->home){
                    $numeropartidos['homevsallpartidos']++;
                }

                //calculo para partidos home @home vs all
                if($partidocorner->home == $partido->home){
                    $numeropartidos['homeathomevsall_partidos']++;
                }

                //calculo para partidos home @away vs all
                if($partidocorner->away == $partido->home){
                    $numeropartidos['homeatawayvsall_partidos']++;
                }

                //calculo para partidos away vs all
                if($partidocorner->home == $partido->away || $partidocorner->away == $partido->away){
                    $numeropartidos['awayvsall_partidos']++;
                }

                //calculo para partidos away @home vs all
                if($partidocorner->home == $partido->away){
                    $numeropartidos['awayathomevsall_partidos']++;
                }

                //calculo para partidos away @away vs all
                if($partidocorner->away == $partido->away){
                    $numeropartidos['awayatawayvsall_partidos']++;
                }
            }  
            
            return $numeropartidos;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    private static function definirProbabilidades($param){
        $numeropartidos = $param['numeropartidos'];
        $cumple = $param['cumple'];
        $prom_corners = $param['prom_corners'];
        $total_mejoropt1 = 0;
        $total_mejoropt2 = 0;
        

        $probabilidades = array();
        
        //BEGIN calculo para partidos | same fixture
        //opt1
        $probabilidades['cumple_todos_homevsaway_opt1'] = $cumple['todos_homevsaway_opt1'];
        $probabilidades['porc_cumple_todos_homevsaway_opt1'] = ($numeropartidos['todoshomevsawaypartidos'] == 0) ? 0 : round(($cumple['todos_homevsaway_opt1'] * 100) / $numeropartidos['todoshomevsawaypartidos']);
        $probabilidades['color_cumple_todos_homevsaway_opt1'] = self::definirBgColor($probabilidades['porc_cumple_todos_homevsaway_opt1']);

        //opt2 | same fixture
        $probabilidades['cumple_todos_homevsaway_opt2'] = $cumple['todos_homevsaway_opt2'];
        $probabilidades['porc_cumple_todos_homevsaway_opt2'] = ($numeropartidos['todoshomevsawaypartidos'] == 0) ? 0 : round(($cumple['todos_homevsaway_opt2'] * 100) / $numeropartidos['todoshomevsawaypartidos']);
        $probabilidades['color_cumple_todos_homevsaway_opt2'] = self::definirBgColor($probabilidades['porc_cumple_todos_homevsaway_opt2']);

        //calcular cual es la mejor opcion | same fixture
        $param_mejor = array();
        $param_mejor['numeropartidos'] = $numeropartidos['todoshomevsawaypartidos'];
        $param_mejor['porc_opt1'] = $probabilidades['porc_cumple_todos_homevsaway_opt1'];
        $param_mejor['porc_opt2'] = $probabilidades['porc_cumple_todos_homevsaway_opt2'];
        
        $resultado_mejor = self::definirMejor($param_mejor);

        $probabilidades['mejor_cumple_todos_homevsaway_opt1'] = $resultado_mejor['opt1'];
        $probabilidades['mejor_cumple_todos_homevsaway_opt2'] = $resultado_mejor['opt2'];

        $total_mejoropt1 += $resultado_mejor['opt1'];
        $total_mejoropt2 += $resultado_mejor['opt2'];
        //END calculo para partidos | same fixture

        //BEGIN calculo para partidos | home @home vs away
        //opt1
        $probabilidades['cumple_homeathomevsaway_opt1'] = $cumple['homeathomevsaway_opt1'];
        $probabilidades['porc_cumple_homeathomevsaway_opt1'] = ($numeropartidos['homeathomevsawaypartidos'] == 0) ? 0 : round(($cumple['homeathomevsaway_opt1'] * 100) / $numeropartidos['homeathomevsawaypartidos']);
        $probabilidades['color_cumple_homeathomevsaway_opt1'] = self::definirBgColor($probabilidades['porc_cumple_homeathomevsaway_opt1']);

        //opt2 | home @home vs away
        $probabilidades['cumple_homeathomevsaway_opt2'] = $cumple['homeathomevsaway_opt2'];
        $probabilidades['porc_cumple_homeathomevsaway_opt2'] = ($numeropartidos['homeathomevsawaypartidos'] == 0) ? 0 : round(($cumple['homeathomevsaway_opt2'] * 100) / $numeropartidos['homeathomevsawaypartidos']);
        $probabilidades['color_cumple_homeathomevsaway_opt2'] = self::definirBgColor($probabilidades['porc_cumple_homeathomevsaway_opt2']);

        //calcular cual es la mejor opcion | home @home vs away
        $param_mejor = array();
        $param_mejor['numeropartidos'] = $numeropartidos['homeathomevsawaypartidos'];
        $param_mejor['porc_opt1'] = $probabilidades['porc_cumple_homeathomevsaway_opt1'];
        $param_mejor['porc_opt2'] = $probabilidades['porc_cumple_homeathomevsaway_opt2'];
        
        $resultado_mejor = self::definirMejor($param_mejor);

        $probabilidades['mejor_cumple_homeathomevsaway_opt1'] = $resultado_mejor['opt1'];
        $probabilidades['mejor_cumple_homeathomevsaway_opt2'] = $resultado_mejor['opt2'];

        $total_mejoropt1 += $resultado_mejor['opt1'];
        $total_mejoropt2 += $resultado_mejor['opt2'];
        
        //calculo para partidos | home @away vs away
        //opt1
        $probabilidades['cumple_homeatawayvsaway_opt1'] = $cumple['homeatawayvsaway_opt1'];
        $probabilidades['porc_cumple_homeatawayvsaway_opt1'] = ($numeropartidos['homeatawayvsawaypartidos'] == 0) ? 0 : round(($cumple['homeatawayvsaway_opt1'] * 100) / $numeropartidos['homeatawayvsawaypartidos']);
        $probabilidades['color_cumple_homeatawayvsaway_opt1'] = self::definirBgColor($probabilidades['porc_cumple_homeatawayvsaway_opt1']);

        //opt2 home @away vs away
        $probabilidades['cumple_homeatawayvsaway_opt2'] = $cumple['homeatawayvsaway_opt2'];
        $probabilidades['porc_cumple_homeatawayvsaway_opt2'] = ($numeropartidos['homeatawayvsawaypartidos'] == 0) ? 0 : round(($cumple['homeatawayvsaway_opt2'] * 100) / $numeropartidos['homeatawayvsawaypartidos']);
        $probabilidades['color_cumple_homeatawayvsaway_opt2'] = self::definirBgColor($probabilidades['porc_cumple_homeatawayvsaway_opt2']);

        //calcular cual es la mejor opcion | home @away vs away
        $param_mejor = array();
        $param_mejor['numeropartidos'] = $numeropartidos['homeatawayvsawaypartidos'];
        $param_mejor['porc_opt1'] = $probabilidades['porc_cumple_homeatawayvsaway_opt1'];
        $param_mejor['porc_opt2'] = $probabilidades['porc_cumple_homeatawayvsaway_opt2'];
        
        $resultado_mejor = self::definirMejor($param_mejor);

        $probabilidades['mejor_cumple_homeatawayvsaway_opt1'] = $resultado_mejor['opt1'];
        $probabilidades['mejor_cumple_homeatawayvsaway_opt2'] = $resultado_mejor['opt2'];

        $total_mejoropt1 += $resultado_mejor['opt1'];
        $total_mejoropt2 += $resultado_mejor['opt2'];

        //calculo para partidos | home vs all
        //opt1
        $probabilidades['cumple_homevsall_opt1'] = $cumple['homevsall_opt1'];
        $probabilidades['porc_cumple_homevsall_opt1'] = ($numeropartidos['homevsallpartidos'] == 0) ? 0 : round(($cumple['homevsall_opt1'] * 100) / $numeropartidos['homevsallpartidos']);
        $probabilidades['color_cumple_homevsall_opt1'] = self::definirBgColor($probabilidades['porc_cumple_homevsall_opt1']);

        //opt2 | home vs all
        $probabilidades['cumple_homevsall_opt2'] = $cumple['homevsall_opt2'];
        $probabilidades['porc_cumple_homevsall_opt2'] = ($numeropartidos['homevsallpartidos'] == 0) ? 0 : round(($cumple['homevsall_opt2'] * 100) / $numeropartidos['homevsallpartidos']);
        $probabilidades['color_cumple_homevsall_opt2'] = self::definirBgColor($probabilidades['porc_cumple_homevsall_opt2']);

        //calcular cual es la mejor opcion | home vs all
        $param_mejor = array();
        $param_mejor['numeropartidos'] = $numeropartidos['homevsallpartidos'];
        $param_mejor['porc_opt1'] = $probabilidades['porc_cumple_homevsall_opt1'];
        $param_mejor['porc_opt2'] = $probabilidades['porc_cumple_homevsall_opt2'];
        
        $resultado_mejor = self::definirMejor($param_mejor);

        $probabilidades['mejor_cumple_homevsall_opt1'] = $resultado_mejor['opt1'];
        $probabilidades['mejor_cumple_homevsall_opt2'] = $resultado_mejor['opt2'];

        $total_mejoropt1 += $resultado_mejor['opt1'];
        $total_mejoropt2 += $resultado_mejor['opt2'];

        //calculo para partidos home @home vs all
        //opt1
        $probabilidades['cumple_homeathomevsall_opt1'] = $cumple['homeathomevsall_opt1'];
        $probabilidades['porc_cumple_homeathomevsall_opt1'] = ($numeropartidos['homeathomevsall_partidos'] == 0) ? 0 : round(($cumple['homeathomevsall_opt1'] * 100) / $numeropartidos['homeathomevsall_partidos']);
        $probabilidades['color_cumple_homeathomevsall_opt1'] = self::definirBgColor($probabilidades['porc_cumple_homeathomevsall_opt1']);

        //opt2 home @home vs all
        $probabilidades['cumple_homeathomevsall_opt2'] = $cumple['homeathomevsall_opt2'];
        $probabilidades['porc_cumple_homeathomevsall_opt2'] = ($numeropartidos['homeathomevsall_partidos'] == 0) ? 0 : round(($cumple['homeathomevsall_opt2'] * 100) / $numeropartidos['homeathomevsall_partidos']);
        $probabilidades['color_cumple_homeathomevsall_opt2'] = self::definirBgColor($probabilidades['porc_cumple_homeathomevsall_opt2']);

        //calcular cual es la mejor opcion - home @home vs all
        $param_mejor = array();
        $param_mejor['numeropartidos'] = $numeropartidos['homevsallpartidos'];
        $param_mejor['porc_opt1'] = $probabilidades['porc_cumple_homeathomevsall_opt1'];
        $param_mejor['porc_opt2'] = $probabilidades['porc_cumple_homeathomevsall_opt2'];
        
        $resultado_mejor = self::definirMejor($param_mejor);

        $probabilidades['mejor_cumple_homeathomevsall_opt1'] = $resultado_mejor['opt1'];
        $probabilidades['mejor_cumple_homeathomevsall_opt2'] = $resultado_mejor['opt2'];

        $total_mejoropt1 += $resultado_mejor['opt1'];
        $total_mejoropt2 += $resultado_mejor['opt2'];
        
        //calculo para partidos home @away vs all
        //opt1
        $probabilidades['cumple_homeatawayvsall_opt1'] = $cumple['homeatawayvsall_opt1'];
        $probabilidades['porc_cumple_homeatawayvsall_opt1'] = ($numeropartidos['homeatawayvsall_partidos'] == 0) ? 0 : round(($cumple['homeatawayvsall_opt1'] * 100) / $numeropartidos['homeatawayvsall_partidos']);
        $probabilidades['color_cumple_homeatawayvsall_opt1'] = self::definirBgColor($probabilidades['porc_cumple_homeatawayvsall_opt1']);

        //opt2 home @away vs all
        $probabilidades['cumple_homeatawayvsall_opt2'] = $cumple['homeatawayvsall_opt2'];
        $probabilidades['porc_cumple_homeatawayvsall_opt2'] = ($numeropartidos['homeatawayvsall_partidos'] == 0) ? 0 : round(($cumple['homeatawayvsall_opt2'] * 100) / $numeropartidos['homeatawayvsall_partidos']);
        $probabilidades['color_cumple_homeatawayvsall_opt2'] = self::definirBgColor($probabilidades['porc_cumple_homeatawayvsall_opt2']);

        //calcular cual es la mejor opcion - home @away vs all
        $param_mejor = array();
        $param_mejor['numeropartidos'] = $numeropartidos['homeatawayvsall_partidos'];
        $param_mejor['porc_opt1'] = $probabilidades['porc_cumple_homeatawayvsall_opt1'];
        $param_mejor['porc_opt2'] = $probabilidades['porc_cumple_homeatawayvsall_opt2'];
        
        $resultado_mejor = self::definirMejor($param_mejor);

        $probabilidades['mejor_cumple_homeatawayvsall_opt1'] = $resultado_mejor['opt1'];
        $probabilidades['mejor_cumple_homeatawayvsall_opt2'] = $resultado_mejor['opt2'];

        $total_mejoropt1 += $resultado_mejor['opt1'];
        $total_mejoropt2 += $resultado_mejor['opt2'];

        //calculo para partidos away vs all
        //opt1
        $probabilidades['cumple_awayvsall_opt1'] = $cumple['awayvsall_opt1'];
        $probabilidades['porc_cumple_awayvsall_opt1'] = ($numeropartidos['awayvsall_partidos'] == 0) ? 0 : round(($cumple['awayvsall_opt1'] * 100) / $numeropartidos['awayvsall_partidos']);
        $probabilidades['color_cumple_awayvsall_opt1'] = self::definirBgColor($probabilidades['porc_cumple_awayvsall_opt1']);

        //opt2 away vs all
        $probabilidades['cumple_awayvsall_opt2'] = $cumple['awayvsall_opt2'];
        $probabilidades['porc_cumple_awayvsall_opt2'] = ($numeropartidos['awayvsall_partidos'] == 0) ? 0 : round(($cumple['awayvsall_opt2'] * 100) / $numeropartidos['awayvsall_partidos']);
        $probabilidades['color_cumple_awayvsall_opt2'] = self::definirBgColor($probabilidades['porc_cumple_awayvsall_opt2']);

        //calcular cual es la mejor opcion - away vs all
        $param_mejor = array();
        $param_mejor['numeropartidos'] = $numeropartidos['awayvsall_partidos'];
        $param_mejor['porc_opt1'] = $probabilidades['porc_cumple_awayvsall_opt1'];
        $param_mejor['porc_opt2'] = $probabilidades['porc_cumple_awayvsall_opt2'];
        
        $resultado_mejor = self::definirMejor($param_mejor);

        $probabilidades['mejor_cumple_awayvsall_opt1'] = $resultado_mejor['opt1'];
        $probabilidades['mejor_cumple_awayvsall_opt2'] = $resultado_mejor['opt2'];

        $total_mejoropt1 += $resultado_mejor['opt1'];
        $total_mejoropt2 += $resultado_mejor['opt2'];

        //calculo para partidos away @home vs all
        //opt1
        $probabilidades['cumple_awayathomevsall_opt1'] = $cumple['awayathomevsall_opt1'];
        $probabilidades['porc_cumple_awayathomevsall_opt1'] = ($numeropartidos['awayathomevsall_partidos'] == 0) ? 0 : round(($cumple['awayathomevsall_opt1'] * 100) / $numeropartidos['awayathomevsall_partidos']);
        $probabilidades['color_cumple_awayathomevsall_opt1'] = self::definirBgColor($probabilidades['porc_cumple_awayathomevsall_opt1']);

        //opt2 away @home vs all
        $probabilidades['cumple_awayathomevsall_opt2'] = $cumple['awayathomevsall_opt2'];
        $probabilidades['porc_cumple_awayathomevsall_opt2'] = ($numeropartidos['awayathomevsall_partidos'] == 0) ? 0 : round(($cumple['awayathomevsall_opt2'] * 100) / $numeropartidos['awayathomevsall_partidos']);
        $probabilidades['color_cumple_awayathomevsall_opt2'] = self::definirBgColor($probabilidades['porc_cumple_awayathomevsall_opt2']);

        //calcular cual es la mejor opcion - away @home vs all
        $param_mejor = array();
        $param_mejor['numeropartidos'] = $numeropartidos['awayvsall_partidos'];
        $param_mejor['porc_opt1'] = $probabilidades['porc_cumple_awayathomevsall_opt1'];
        $param_mejor['porc_opt2'] = $probabilidades['porc_cumple_awayathomevsall_opt2'];
        
        $resultado_mejor = self::definirMejor($param_mejor);

        $probabilidades['mejor_cumple_awayathomevsall_opt1'] = $resultado_mejor['opt1'];
        $probabilidades['mejor_cumple_awayathomevsall_opt2'] = $resultado_mejor['opt2'];

        $total_mejoropt1 += $resultado_mejor['opt1'];
        $total_mejoropt2 += $resultado_mejor['opt2'];

        //calculo para partidos away @away vs all
        //opt1
        $probabilidades['cumple_awayatawayvsall_opt1'] = $cumple['awayatawayvsall_opt1'];
        $probabilidades['porc_cumple_awayatawayvsall_opt1'] = ($numeropartidos['awayatawayvsall_partidos'] == 0) ? 0 : round(($cumple['awayatawayvsall_opt1'] * 100) / $numeropartidos['awayatawayvsall_partidos']);
        $probabilidades['color_cumple_awayatawayvsall_opt1'] = self::definirBgColor($probabilidades['porc_cumple_awayatawayvsall_opt1']);

        //opt2 away @away vs all
        $probabilidades['cumple_awayatawayvsall_opt2'] = $cumple['awayatawayvsall_opt2'];
        $probabilidades['porc_cumple_awayatawayvsall_opt2'] = ($numeropartidos['awayatawayvsall_partidos'] == 0) ? 0 : round(($cumple['awayatawayvsall_opt2'] * 100) / $numeropartidos['awayatawayvsall_partidos']);
        $probabilidades['color_cumple_awayatawayvsall_opt2'] = self::definirBgColor($probabilidades['porc_cumple_awayatawayvsall_opt2']);

        //calcular cual es la mejor opcion - away @away vs all
        $param_mejor = array();
        $param_mejor['numeropartidos'] = $numeropartidos['awayatawayvsall_partidos'];
        $param_mejor['porc_opt1'] = $probabilidades['porc_cumple_awayatawayvsall_opt1'];
        $param_mejor['porc_opt2'] = $probabilidades['porc_cumple_awayatawayvsall_opt2'];
        
        $resultado_mejor = self::definirMejor($param_mejor);

        $probabilidades['mejor_cumple_awayatawayvsall_opt1'] = $resultado_mejor['opt1'];
        $probabilidades['mejor_cumple_awayatawayvsall_opt2'] = $resultado_mejor['opt2'];

        $total_mejoropt1 += $resultado_mejor['opt1'];
        $total_mejoropt2 += $resultado_mejor['opt2'];

        $probabilidades['total_mejoropt1'] = $total_mejoropt1;
        $probabilidades['total_mejoropt2'] = $total_mejoropt2;

        return $probabilidades;
    }

    /**
     * @throws Exception
     */
    public static function definirPromedioCorners($param): array
    {
        try {
            $prom_corners = $param['prom_corners'];
            $numeropartidos = $param['numeropartidos'];

            //calcular promedio de corners | same fixture
            if($numeropartidos['todoshomevsawaypartidos'] == 0){
                $prom_corners['avg_cornershome_todos_homevsaway'] = 0;
                $prom_corners['avg_cornersaway_todos_homevsaway'] = 0;
                $prom_corners['avg_cornerstotal_todos_homevsaway'] = 0;
            } else{
                $prom_corners['avg_cornershome_todos_homevsaway'] = round($prom_corners['num_cornershome_todos_homevsaway'] / $numeropartidos['todoshomevsawaypartidos'], 2);
                $prom_corners['avg_cornersaway_todos_homevsaway'] = round($prom_corners['num_cornersaway_todos_homevsaway'] / $numeropartidos['todoshomevsawaypartidos'], 2);
                $prom_corners['avg_cornerstotal_todos_homevsaway'] = round($prom_corners['num_cornerstotal_todos_homevsaway'] / $numeropartidos['todoshomevsawaypartidos'], 2);
            }

            //calcular promedio de corners | home @home vs away
            if($numeropartidos['homeathomevsawaypartidos'] == 0){
                $prom_corners['avg_cornershome_homeathomevsaway'] = 0;
                $prom_corners['avg_cornersaway_homeathomevsaway'] = 0;
                $prom_corners['avg_cornerstotal_homeathomevsaway'] = 0;
            } else{
                $prom_corners['avg_cornershome_homeathomevsaway'] = round($prom_corners['num_cornershome_homeathomevsaway'] / $numeropartidos['homeathomevsawaypartidos'], 2);
                $prom_corners['avg_cornersaway_homeathomevsaway'] = round($prom_corners['num_cornersaway_homeathomevsaway'] / $numeropartidos['homeathomevsawaypartidos'], 2);
                $prom_corners['avg_cornerstotal_homeathomevsaway'] = round($prom_corners['num_cornerstotal_homeathomevsaway'] / $numeropartidos['homeathomevsawaypartidos'], 2);
            }

            //calcular promedio de corners | home @away vs away
            $prom_corners['avg_cornershome_homeatawayvsaway'] = round($prom_corners['num_cornershome_homeatawayvsaway'] / $numeropartidos['homeatawayvsawaypartidos'], 2);
            $prom_corners['avg_cornersaway_homeatawayvsaway'] = round($prom_corners['num_cornersaway_homeatawayvsaway'] / $numeropartidos['homeatawayvsawaypartidos'], 2);
            $prom_corners['avg_cornerstotal_homeatawayvsaway'] = round($prom_corners['num_cornerstotal_homeatawayvsaway'] / $numeropartidos['homeatawayvsawaypartidos'], 2);

            //calcular promedio de corners | home vs all
            $prom_corners['avg_cornershome_homevsall'] = round($prom_corners['num_cornershome_homevsall'] / $numeropartidos['homevsallpartidos'], 2);
            $prom_corners['avg_cornersaway_homevsall'] = round($prom_corners['num_cornersaway_homevsall'] / $numeropartidos['homevsallpartidos'], 2);
            $prom_corners['avg_cornerstotal_homevsall'] = round($prom_corners['num_cornerstotal_homevsall'] / $numeropartidos['homevsallpartidos'], 2);
    
            //calcular promedio de corners | home @home vs all
            $prom_corners['avg_cornershome_homeathomevsall'] = round($prom_corners['num_cornershome_homeathomevsall'] / $numeropartidos['homeathomevsall_partidos'], 2);
            $prom_corners['avg_cornersaway_homeathomevsall'] = round($prom_corners['num_cornersaway_homeathomevsall'] / $numeropartidos['homeathomevsall_partidos'], 2);
            $prom_corners['avg_cornerstotal_homeathomevsall'] = round($prom_corners['num_cornerstotal_homeathomevsall'] / $numeropartidos['homeathomevsall_partidos'], 2);

            //calcular promedio de corners | home @away vs all
            $prom_corners['avg_cornershome_homeatawayvsall'] = round($prom_corners['num_cornershome_homeatawayvsall'] / $numeropartidos['homeatawayvsall_partidos'], 2);
            $prom_corners['avg_cornersaway_homeatawayvsall'] = round($prom_corners['num_cornersaway_homeatawayvsall'] / $numeropartidos['homeatawayvsall_partidos'], 2);
            $prom_corners['avg_cornerstotal_homeatawayvsall'] = round($prom_corners['num_cornerstotal_homeatawayvsall'] / $numeropartidos['homeatawayvsall_partidos'], 2);

            //calcular promedio de corners | away vs all
            $prom_corners['avg_cornershome_awayvsall'] = round($prom_corners['num_cornershome_awayvsall'] / $numeropartidos['awayvsall_partidos'], 2);
            $prom_corners['avg_cornersaway_awayvsall'] = round($prom_corners['num_cornersaway_awayvsall'] / $numeropartidos['awayvsall_partidos'], 2);
            $prom_corners['avg_cornerstotal_awayvsall'] = round($prom_corners['num_cornerstotal_awayvsall'] / $numeropartidos['awayvsall_partidos'], 2);
            
            //calcular promedio de corners | away @home vs all
            $prom_corners['avg_cornershome_awayathomevsall'] = round($prom_corners['num_cornershome_awayathomevsall'] / $numeropartidos['awayathomevsall_partidos'], 2);
            $prom_corners['avg_cornersaway_awayathomevsall'] = round($prom_corners['num_cornersaway_awayathomevsall'] / $numeropartidos['awayathomevsall_partidos'], 2);
            $prom_corners['avg_cornerstotal_awayathomevsall'] = round($prom_corners['num_cornerstotal_awayathomevsall'] / $numeropartidos['awayathomevsall_partidos'], 2);

            //calcular promedio de corners | away @away vs all
            $prom_corners['avg_cornershome_awayatawayvsall'] = round($prom_corners['num_cornershome_awayatawayvsall'] / $numeropartidos['awayatawayvsall_partidos'], 2);
            $prom_corners['avg_cornersaway_awayatawayvsall'] = round($prom_corners['num_cornersaway_awayatawayvsall'] / $numeropartidos['awayatawayvsall_partidos'], 2);
            $prom_corners['avg_cornerstotal_awayatawayvsall'] = round($prom_corners['num_cornerstotal_awayatawayvsall'] / $numeropartidos['awayatawayvsall_partidos'], 2);

            return $prom_corners;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function definirPromedioCornerCumple($param): array
    {
        try {
            $probabilidades = $param['probabilidades'];
            $prom_corners = $param['prom_corners'];
            $masxtotalcorners = $param['masxtotalcorners'];
            $menosxtotalcorners = $param['menosxtotalcorners'];
            $masxcornershome = $param['masxcornershome'];
            $menosxcornershome = $param['menosxcornershome'];
            $masxcornersaway = $param['masxcornersaway'];
            $menosxcornersaway = $param['menosxcornersaway'];
            $promcorners_cumple = self::constructArrayPromCornerCumple();

            //calcular si el promedio de coners es mayor/menor al digitado | totalcorners | H vs A
            if($prom_corners['avg_cornerstotal_todos_homevsaway'] > $masxtotalcorners){
                $promcorners_cumple['masxtotalcorners_cornerstotal_todos_homevsaway'] = 1;
            }
            if($prom_corners['avg_cornerstotal_todos_homevsaway'] < $menosxtotalcorners){
                $promcorners_cumple['menosxtotalcorners_cornerstotal_todos_homevsaway'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | totalcorners | H @H vs A
            if($prom_corners['avg_cornerstotal_homeathomevsaway'] > $masxtotalcorners){
                $promcorners_cumple['masxtotalcorners_cornerstotal_homeathomevsaway'] = 1;
            }
            if($prom_corners['avg_cornerstotal_homeathomevsaway'] < $menosxtotalcorners){
                $promcorners_cumple['menosxtotalcorners_cornerstotal_homeathomevsaway'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | totalcorners | H @A vs A
            if($prom_corners['avg_cornerstotal_homeatawayvsaway'] > $masxtotalcorners){
                $promcorners_cumple['masxtotalcorners_cornerstotal_homeatawayvsaway'] = 1;
            }
            if($prom_corners['avg_cornerstotal_homeatawayvsaway'] < $menosxtotalcorners){
                $promcorners_cumple['menosxtotalcorners_cornerstotal_homeatawayvsaway'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | totalcorners | H vs All
            if($prom_corners['avg_cornerstotal_homevsall'] > $masxtotalcorners){
                $promcorners_cumple['masxtotalcorners_cornerstotal_homevsall'] = 1;
            }
            if($prom_corners['avg_cornerstotal_homevsall'] < $menosxtotalcorners){
                $promcorners_cumple['menosxtotalcorners_cornerstotal_homevsall'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | totalcorners | H @H vs All
            if($prom_corners['avg_cornerstotal_homeathomevsall'] > $masxtotalcorners){
                $promcorners_cumple['masxtotalcorners_cornerstotal_homeathomevsall'] = 1;
            }
            if($prom_corners['avg_cornerstotal_homeathomevsall'] < $menosxtotalcorners){
                $promcorners_cumple['menosxtotalcorners_cornerstotal_homeathomevsall'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | totalcorners | H @A vs All
            if($prom_corners['avg_cornerstotal_homeatawayvsall'] > $masxtotalcorners){
                $promcorners_cumple['masxtotalcorners_cornerstotal_homeatawayvsall'] = 1;
            }
            if($prom_corners['avg_cornerstotal_homeatawayvsall'] < $menosxtotalcorners){
                $promcorners_cumple['menosxtotalcorners_cornerstotal_homeatawayvsall'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | totalcorners | A vs All
            if($prom_corners['avg_cornerstotal_awayvsall'] > $masxtotalcorners){
                $promcorners_cumple['masxtotalcorners_cornerstotal_awayvsall'] = 1;
            }
            if($prom_corners['avg_cornerstotal_awayvsall'] < $menosxtotalcorners){
                $promcorners_cumple['menosxtotalcorners_cornerstotal_awayvsall'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | totalcorners | A @H vs All
            if($prom_corners['avg_cornerstotal_awayathomevsall'] > $masxtotalcorners){
                $promcorners_cumple['masxtotalcorners_cornerstotal_awayathomevsall'] = 1;
            }
            if($prom_corners['avg_cornerstotal_awayathomevsall'] < $menosxtotalcorners){
                $promcorners_cumple['menosxtotalcorners_cornerstotal_awayathomevsall'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | totalcorners | A @A vs All
            if($prom_corners['avg_cornerstotal_awayatawayvsall'] > $masxtotalcorners){
                $promcorners_cumple['masxtotalcorners_cornerstotal_awayatawayvsall'] = 1;
            }
            if($prom_corners['avg_cornerstotal_awayatawayvsall'] < $menosxtotalcorners){
                $promcorners_cumple['menosxtotalcorners_cornerstotal_awayatawayvsall'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornershome | same fixture
            if($prom_corners['avg_cornershome_todos_homevsaway'] > $masxcornershome){
                $promcorners_cumple['masxcornershome_cornerstotal_todos_homevsaway'] = 1;
            }
            if($prom_corners['avg_cornershome_todos_homevsaway'] < $menosxcornershome){
                $promcorners_cumple['menosxcornershome_cornerstotal_todos_homevsaway'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornershome | H @H vs A
            if($prom_corners['avg_cornershome_homeathomevsaway'] > $masxcornershome){
                $promcorners_cumple['masxcornershome_cornerstotal_homeathomevsaway'] = 1;
            }
            if($prom_corners['avg_cornershome_homeathomevsaway'] < $menosxcornershome){
                $promcorners_cumple['menosxcornershome_cornerstotal_homeathomevsaway'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornershome | H @A vs A
            if($prom_corners['avg_cornersaway_homeatawayvsaway'] > $masxcornershome){
                $promcorners_cumple['masxcornershome_cornerstotal_homeatawayvsaway'] = 1;
            }
            if($prom_corners['avg_cornersaway_homeatawayvsaway'] < $menosxcornershome){
                $promcorners_cumple['menosxcornershome_cornerstotal_homeatawayvsaway'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornershome | H @H vs All
            if($prom_corners['avg_cornershome_homeathomevsall'] > $masxcornershome){
                $promcorners_cumple['masxcornershome_cornerstotal_homeathomevsall'] = 1;
            }
            if($prom_corners['avg_cornershome_homeathomevsall'] < $menosxcornershome){
                $promcorners_cumple['menosxcornershome_cornerstotal_homeathomevsall'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornershome | H @A vs All
            if($prom_corners['avg_cornersaway_homeatawayvsall'] > $masxcornershome){
                $promcorners_cumple['masxcornershome_cornerstotal_homeatawayvsall'] = 1;
            }
            if($prom_corners['avg_cornersaway_homeatawayvsall'] < $menosxcornershome){
                $promcorners_cumple['menosxcornershome_cornerstotal_homeatawayvsall'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornersaway | H vs A
            if($prom_corners['avg_cornersaway_todos_homevsaway'] > $masxcornersaway){
                $promcorners_cumple['masxcornersaway_cornerstotal_todos_homevsaway'] = 1;
            }
            if($prom_corners['avg_cornersaway_todos_homevsaway'] < $menosxcornersaway){
                $promcorners_cumple['menosxcornersaway_cornerstotal_todos_homevsaway'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornersaway | H @H vs A
            if($prom_corners['avg_cornersaway_homeathomevsaway'] > $masxcornersaway){
                $promcorners_cumple['masxcornersaway_cornerstotal_homeathomevsaway'] = 1;
            }
            if($prom_corners['avg_cornersaway_homeathomevsaway'] < $menosxcornersaway){
                $promcorners_cumple['menosxcornersaway_cornerstotal_homeathomevsaway'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornersaway | H @A vs A
            if($prom_corners['avg_cornershome_homeatawayvsaway'] > $masxcornersaway){
                $promcorners_cumple['masxcornersaway_cornerstotal_homeatawayvsaway'] = 1;
            }
            if($prom_corners['avg_cornershome_homeatawayvsaway'] < $menosxcornersaway){
                $promcorners_cumple['menosxcornersaway_cornerstotal_homeatawayvsaway'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornersaway | A @H vs All
            if($prom_corners['avg_cornershome_awayathomevsall'] > $masxcornersaway){
                $promcorners_cumple['masxcornersaway_cornerstotal_awayathomevsall'] = 1;
            }
            if($prom_corners['avg_cornershome_awayathomevsall'] < $menosxcornersaway){
                $promcorners_cumple['menosxcornersaway_cornerstotal_awayathomevsall'] = 1;
            }

            //calcular si el promedio de coners es mayor/menor al digitado | cornersaway | A @A vs All
            if($prom_corners['avg_cornersaway_awayatawayvsall'] > $masxcornersaway){
                $promcorners_cumple['masxcornersaway_cornerstotal_awayatawayvsall'] = 1;
            }
            if($prom_corners['avg_cornersaway_awayatawayvsall'] < $menosxcornersaway){
                $promcorners_cumple['menosxcornersaway_cornerstotal_awayatawayvsall'] = 1;
            }

            return $promcorners_cumple;
    
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    private static function definirBgColor($porc_opt1): string
    {
        if($porc_opt1 > self::VALUETOGREENSTRONG){
            return self::BGTOGREENSTRONG;

        } elseif($porc_opt1 > self::VALUETOGREEN && $porc_opt1 <= self::VALUETOGREENSTRONG){
            return self::BGTOGREEN;

        } elseif($porc_opt1 > self::VALUETOYELLOW && $porc_opt1 <= self::VALUETOGREEN){
            return self::BGTOYELLOW;

        } else{
            return '';
        }
    }

    private static function definirMejor($param):array
    {
        $numeropartidos = $param['numeropartidos'];
        $porc_opt1 = $param['porc_opt1'];
        $porc_opt2 = $param['porc_opt2'];
        
        $mejor = array();
        $mejoropt1 = 0;
        $mejoropt2 = 0;

        if($numeropartidos >= 7){
            if($porc_opt1 > $porc_opt2){
                $mejoropt1 = 1;
                $mejoropt2 = 0;

            } elseif($porc_opt1 == $porc_opt2){
                $mejoropt1 = 0;
                $mejoropt2 = 0;

            } else{
                $mejoropt1 = 0;
                $mejoropt2 = 1;
            }
        } else{
            $mejoropt1 = 0;
            $mejoropt2 = 0;
        }

        $mejor['opt1'] = $mejoropt1;
        $mejor['opt2'] = $mejoropt2;

        return $mejor;
    }

    public static function asignarValorApuesta($idtipoapuesta, $valorapuesta, $param):array{
        try {
            $idtipoapuesta_ordenado = ordena($idtipoapuesta);

            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MASXTOTALCORNERS){
                $param['masxtotalcorners'] = $valorapuesta;

            } else{
                $param['masxtotalcorners'] = PartidoCorner::DEFAULT_MASXTOTALCORNERS;
            }

            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MENOSXTOTALCORNERS){
                $param['menosxtotalcorners'] = $valorapuesta;

            } else{
                $param['menosxtotalcorners'] = PartidoCorner::DEFAULT_MENOSXTOTALCORNERS;
            }

            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MASXCORNERSHOME){
                $param['masxcornershome'] = $valorapuesta;

            } else{
                $param['masxcornershome'] = PartidoCorner::DEFAULT_MASXCORNERSHOME;
            }

            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MENOSXCORNERSHOME){
                $param['menosxcornershome'] = $valorapuesta;

            } else{
                $param['menosxcornershome'] = PartidoCorner::DEFAULT_MENOSXCORNERSHOME;
            }

            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MASXCORNERSAWAY){
                $param['masxcornersaway'] = $valorapuesta;

            } else{
                $param['masxcornersaway'] = PartidoCorner::DEFAULT_MASXCORNERSAWAY;
            }

            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MENOSXCORNERSAWAY){
                $param['menosxcornersaway'] = $valorapuesta;

            } else{
                $param['menosxcornersaway'] = PartidoCorner::DEFAULT_MENOSXCORNERSAWAY;
            }

            return $param;
        
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    public static function asignarProbabilidadesSeleccionadda($idtipoapuesta, $probabilidades){
        try {
            $idtipoapuesta_ordenado = ordena($idtipoapuesta);

            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MASXTOTALCORNERS){
                $probabilidades_selected = PartidoCorner::constructArrayParaApuesta(1, '', $probabilidades['totalcorners']);
            }
            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MENOSXTOTALCORNERS){
                $probabilidades_selected = PartidoCorner::constructArrayParaApuesta(2, '', $probabilidades['totalcorners']);
            }
        
            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MASCORNERSHOME){
                $probabilidades_selected = PartidoCorner::constructArrayParaApuesta(1, 'AWAY', $probabilidades['mascorners']);
            }
            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MASCORNERSAWAY){
                $probabilidades_selected = PartidoCorner::constructArrayParaApuesta(2, 'HOME', $probabilidades['mascorners']);
            }
        
            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MASXCORNERSHOME){
                $probabilidades_selected = PartidoCorner::constructArrayParaApuesta(1, 'AWAY', $probabilidades['cornershome']);
            }
            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MENOSXCORNERSHOME){
                $probabilidades_selected = PartidoCorner::constructArrayParaApuesta(2, 'AWAY', $probabilidades['cornershome']);
            }
        
            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MASXCORNERSAWAY){
                $probabilidades_selected = PartidoCorner::constructArrayParaApuesta(1, 'HOME', $probabilidades['cornersaway']);
            }
            if($idtipoapuesta_ordenado == ApuestaTipo::ID_MENOSXCORNERSAWAY){
                $probabilidades_selected = PartidoCorner::constructArrayParaApuesta(2, 'HOME', $probabilidades['cornersaway']);
            }

            return $probabilidades_selected;
        
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validateData(): void
    {
        try {
            $this->home = strtoupper($this->home);
            $this->away = strtoupper($this->away);
            $this->torneo = strtoupper($this->torneo);

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>