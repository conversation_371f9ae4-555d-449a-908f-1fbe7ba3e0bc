<?php

class TickerPosSize
{
	public string  $id;
	public float   $buying_power;
	public float   $account_risk;
	public float   $account_risk_valor;
	public float   $porc_after_breakout;
	public float   $porc_stoploss;
	public float   $porc_profit_taking;
	public float   $porc_first_buy;
	public float   $breakout_spot;
	public float   $buy_price;
	public float   $trade_risk;
	public float   $stoploss;
	public float   $profit;
	public float   $position_size;
	public float   $first_buy_position_size;
	private string $bd_table               = 'tickers_pos_size';
	private string $bd_alias               = 'tiposi';
	private string $bd_id                  = 'id';
	private string $bd_buying_power        = 'buying_power';
	private string $bd_account_risk        = 'account_risk';
	private string $bd_porc_after_breakout = 'porc_after_breakout';
	private string $bd_porc_stoploss       = 'porc_stoploss';
	private string $bd_porc_profit_taking  = 'porc_profit_taking';
	private string $bd_porc_first_buy      = 'porc_first_buy';
	
	function __construct()
	{
		$this->id                      = '';
		$this->buying_power            = 0;
		$this->account_risk            = 0;
		$this->account_risk_valor      = 0;
		$this->porc_after_breakout     = 0;
		$this->porc_stoploss           = 0;
		$this->porc_profit_taking      = 0;
		$this->porc_first_buy          = 0;
		$this->breakout_spot           = 0;
		$this->buy_price               = 0;
		$this->trade_risk              = 0;
		$this->stoploss                = 0;
		$this->profit                  = 0;
		$this->position_size           = 0;
		$this->first_buy_position_size = 0;
	}
	
	/**
	 * @param $resultado
	 *
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado): self
	{
		try {
			$cq = new self;
			
			$objeto                      = new self;
			$objeto->id                  = desordena($resultado[$cq->bd_id]);
			$objeto->buying_power        = $resultado[$cq->bd_buying_power];
			$objeto->account_risk        = $resultado[$cq->bd_account_risk];
			$objeto->porc_after_breakout = $resultado[$cq->bd_porc_after_breakout];
			$objeto->porc_stoploss       = $resultado[$cq->bd_porc_stoploss];
			$objeto->porc_profit_taking  = $resultado[$cq->bd_porc_profit_taking];
			$objeto->porc_first_buy      = $resultado[$cq->bd_porc_first_buy];
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get(PDO $conexion): self
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", 1);
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modificar(PDO $conexion): void
	{
		try {
			$this->validar_data();
			$this->calcular_account_risk_valor();
			$this->calcular_buy_price();
			$this->calcular_porc_stoploss();
			$this->calcular_trade_risk();
			//$this->calcular_stoploss(); el stoploss se ingresa manualmente ahora
			$this->calcular_profit();
			$this->calcular_position_size();
			$this->calcular_first_buy_position_size();
			
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_buying_power = :$cq->bd_buying_power ";
			$query .= "  ,$cq->bd_account_risk = :$cq->bd_account_risk ";
			$query .= "  ,$cq->bd_porc_after_breakout = :$cq->bd_porc_after_breakout ";
			$query .= "  ,$cq->bd_porc_stoploss = :$cq->bd_porc_stoploss ";
			$query .= "  ,$cq->bd_porc_profit_taking = :$cq->bd_porc_profit_taking ";
			$query .= "  ,$cq->bd_porc_first_buy = :$cq->bd_porc_first_buy ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_buying_power", $this->buying_power);
			$statement->bindValue(":$cq->bd_account_risk", $this->account_risk);
			$statement->bindValue(":$cq->bd_porc_after_breakout", $this->porc_after_breakout);
			$statement->bindValue(":$cq->bd_porc_stoploss", $this->porc_stoploss);
			$statement->bindValue(":$cq->bd_porc_profit_taking", $this->porc_profit_taking);
			$statement->bindValue(":$cq->bd_porc_first_buy", $this->porc_first_buy);
			$statement->bindValue(":$cq->bd_id", ordena($this->id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function calcular_account_risk_valor(): void
	{
		try {
			$this->account_risk_valor = round($this->buying_power * ($this->account_risk / 100), 2);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function calcular_buy_price(): void
	{
		try {
			$this->buy_price = round($this->breakout_spot * (1 + ($this->porc_after_breakout / 100)), 2);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function calcular_trade_risk(): void
	{
		try {
			$this->trade_risk = round($this->buy_price * ($this->porc_stoploss / 100), 2);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function calcular_stoploss(): void
	{
		try {
			$this->stoploss = round($this->buy_price - $this->trade_risk, 2);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function calcular_porc_stoploss(): void
	{
		try {
			$this->porc_stoploss = round(($this->stoploss * 100) / $this->buy_price, 2);
			
			if ($this->porc_stoploss < 8) {
				$this->porc_stoploss = 8;
			}
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function calcular_profit(): void
	{
		try {
			$this->profit = round($this->buy_price + ($this->buy_price * ($this->porc_profit_taking / 100)), 2);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function calcular_position_size(): void
	{
		try {
			$this->position_size = round($this->account_risk_valor / $this->trade_risk, 2);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function calcular_first_buy_position_size(): void
	{
		try {
			$this->first_buy_position_size = round($this->position_size * ($this->porc_first_buy / 100), 2);
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function validar_data(): void
	{
		try {
			validar_campovacio($this->buying_power, 'Debe especificar el buying power');
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>