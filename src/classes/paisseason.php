<?php

class PaisSeason
{
    public string $id;
    public string $nombre;
    public string $season;
    public float $perc_progress;
    public string $fecha_actualizado;
    public string $dias_desde_actualizado;
    private string $bd_table = 'paises_seasons';
    private string $bd_alias = 'pasea';
    private string $bd_id = 'id_pais_season';
    private string $bd_nombre = 'nombre';
    private string $bd_season = 'season';
    private string $bd_perc_progress = 'perc_progress';
    private string $bd_fecha_actualizado = 'fecha_actualizado';

    function __construct()
    {
        $this->id = '';
        $this->nombre = '';
        $this->season = 0;
        $this->perc_progress = 0;
        $this->fecha_actualizado = '';
        $this->dias_desde_actualizado = '';
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id = desordena($resultado[$cq->bd_id]);
            $objeto->nombre = $resultado[$cq->bd_nombre];
            $objeto->season = $resultado[$cq->bd_season];
            $objeto->perc_progress = $resultado[$cq->bd_perc_progress];
            $objeto->fecha_actualizado = $resultado[$cq->bd_fecha_actualizado];
            $objeto->dias_desde_actualizado = getDateDiffDays($objeto->fecha_actualizado, create_date());

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($paramref, PDO $conexion): self
    {
        try {
            #region region parametros
            $nom_pais = (isset($paramref['nom_pais'])) ? $paramref['nom_pais'] : "";
            $season   = (isset($paramref['season'])) ? $paramref['season'] : "";
            #endregion parametros

            $cq  = new self;
            $cqa = $cq->bd_alias;

            #region region query
            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id > 0 ";

            if (!empty($nom_pais)) {
                $query .= "AND $cq->bd_nombre = :$cq->bd_nombre ";
            }

            if (!empty($season)) {
                $query .= "AND $cq->bd_season = :$cq->bd_season ";
            }
            #endregion query

            $statement = $conexion->prepare($query);

            #region region bindvalue
            if (!empty($nom_pais)) {
                $statement->bindValue(":$cq->bd_nombre", $nom_pais);
            }
            if (!empty($season)) {
                $statement->bindValue(":$cq->bd_season", $season);
            }
            #endregion bindvalue

            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add(PDO $conexion): void
    {
        try {
            $this->validate_data_add();

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_nombre ";
            $query .= "  ,$cq->bd_season ";
            $query .= "  ,$cq->bd_perc_progress ";
            $query .= "  ,$cq->bd_fecha_actualizado ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_nombre ";
            $query .= "  ,:$cq->bd_season ";
            $query .= "  ,:$cq->bd_perc_progress ";
            $query .= "  ,:$cq->bd_fecha_actualizado ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_nombre", $this->nombre);
            $statement->bindValue(":$cq->bd_season", $this->season);
            $statement->bindValue(":$cq->bd_perc_progress", $this->perc_progress);
            $statement->bindValue(":$cq->bd_fecha_actualizado", $this->fecha_actualizado);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modify(PDO $conexion): void
    {
        try {
            $this->validate_data_modify();

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_perc_progress = :$cq->bd_perc_progress ";
            $query .= "  ,$cq->bd_fecha_actualizado = :$cq->bd_fecha_actualizado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_perc_progress", $this->perc_progress);
            $statement->bindValue(":$cq->bd_fecha_actualizado", $this->fecha_actualizado);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data_add(): void
    {
        try {
            validar_textovacio($this->nombre, 'Debe especificar el pais');
            validar_textovacio($this->season, 'Debe especificar el season');

            $this->nombre            = mb_strtoupper($this->nombre);
            $this->fecha_actualizado = create_date();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validate_data_modify(): void
    {
        try {
            $this->fecha_actualizado = create_date();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>