<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region try
try {
    $method_try = 1;
    
    $potencialini = 1.00;
    $maxpotencial = PartidoApuesta::getMaxPotencial($conexion);

    $reporte = array();
    $n = 0;

    for ($i=$potencialini; $i <= ($maxpotencial + 0.10); $i+=0.10) { 
        $potencial1 = $i;
        $potencial2 = $i + 0.09;
        
        $detalle = PartidoApuesta::createReporteWinRatioPotencial($potencial1, $potencial2, $conexion);

        $reporte[$n]['potencial'] = $potencial1 . ' | ' . $potencial2;
        $reporte[$n]['num_apuestas'] = $detalle['num_apuestas'];
        $reporte[$n]['winratio'] = $detalle['winratio'];
        $reporte[$n]['winratio_numapuestas_porcprofit'] = $detalle['winratio_numapuestas_porcprofit'];
        $reporte[$n]['val_apostado'] = $detalle['val_apostado'];
        $reporte[$n]['val_profit'] = $detalle['val_profit'];
        $reporte[$n]['porc_profit'] = $detalle['porc_profit'];

        $n++;
    }

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/cpartidoreporte_winratiopotencial.view.php';

?>