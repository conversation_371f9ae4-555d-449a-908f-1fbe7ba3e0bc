<?php session_start();

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/project.php';
require_once __ROOT__ . '/src/classes/task.php';
require_once __ROOT__ . '/src/general/preparar.php';

$tasks = array();
$tasksdone = array();
$updatelist_tasks = 0;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$id_project = limpiar_datos($_POST['id_project']);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_create'])) {
	try {
		$name = limpiar_datos($_POST['name_project']);
		
		$project = new Project;
		$project->name = strtoupper($name);
		$project->add($conexion);

		$name = "";

		$success_display = 'show';
		$success_text = 'The project has been created.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_search'])) {
	try {
		validar_textovacio($id_project,'Specify project');

		$updatelist_tasks = 1;		

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
	try {
		$name = limpiar_datos($_POST['name']);
		$module = limpiar_datos($_POST['module']);
		$note = limpiar_datos($_POST['note']);

		validar_textovacio($id_project, 'Specify project');
		validar_textovacio($name, 'Specify name');
		validar_textovacio($module, 'Specify module');		

		$task = new Task;
		$task->id_project = $id_project;
		$task->name = $name;
		$task->note = $note;
		$task->module = strtoupper($module);	
		$task->add($conexion);
		
		$name = "";
		$note = "";
		$module = "";

		$updatelist_tasks = 1;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_donetask'])) {
	try {
		$id_task = limpiar_datos($_POST['done_idtask']);

		Task::done($id_task,$conexion);

		$updatelist_tasks = 1;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_worktask'])) {
	try {
		$id_task = limpiar_datos($_POST['work_idtask']);
		
		Task::work($id_task,$conexion);

		$updatelist_tasks = 1;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_deltask'])) {
	try {
		$id_task = limpiar_datos($_POST['del_idtask']);

		Task::delete($id_task,$conexion);

		$updatelist_tasks = 1;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_distask'])) {
	try {
		$id_task = limpiar_datos($_POST['dis_idtask']);

		Task::disregard($id_task,$conexion);

		$updatelist_tasks = 1;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_modtask'])) {
	try {
		$id_task = limpiar_datos($_POST['modtask_id_task']);
		$edit_name = limpiar_datos($_POST['edit_name']);
		$edit_module = limpiar_datos($_POST['edit_module']);
		$edit_note = limpiar_datos($_POST['edit_note']);

		$task = new Task;
		$task->id_task = $id_task;
		$task->name = $edit_name;
		$task->module = strtoupper($edit_module);
		$task->note = $edit_note;
		$task->modify($conexion);

		$updatelist_tasks = 1;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_urgtask'])) {
	try {
		$id_task = limpiar_datos($_POST['modtask_id_task']);
		
		Task::urgent($id_task,$conexion);
		
		$updatelist_tasks = 1;
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}

try {
	$projects = Project::get_list($conexion);

	if($updatelist_tasks == 1){
		$tasks = Task::get_list($id_project,$isdone=0,$conexion);
		$tasksdone = Task::get_list($id_project,$isdone=1,$conexion);
	}			
} catch (Exception $e) {
	$error_display = 'show';
	$error_text = $e->getMessage();
}

require_once __ROOT__ . '/views/atasks.view.php';

?>
