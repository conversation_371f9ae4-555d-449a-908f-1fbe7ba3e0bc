<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/general/preparar.php';

//init variables
$id_cupon   = '';
$id_apuesta = '';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {


    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $id_apuesta = limpiar_datos($_POST['id_apuesta']);
        $id_cupon = limpiar_datos($_POST['id_cupon']);

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_search
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_search'])) {
    try {
        $id_apuesta = PartidoApuesta::get_byid_cupon($id_cupon, $conexion);

        if(empty($id_apuesta)){
            throw new Exception('No se encontro la apuesta');
        }
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_search
#region sub_ganado
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_ganado'])) {
    try {
        $modpartidoapuesta = new PartidoApuesta;
        $modpartidoapuesta->id          = $id_apuesta;
        $modpartidoapuesta->is_resuelto = 1;
        $modpartidoapuesta->isganado    = 1;
        $modpartidoapuesta->modifyGanancia($conexion);

        $success_display = 'show';
        $success_text = 'Apuesta marcada como ganada.';

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_ganado
#region sub_perdido
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_perdido'])) {
    try {
        $modpartidoapuesta = new PartidoApuesta;
        $modpartidoapuesta->id          = $id_apuesta;
        $modpartidoapuesta->is_resuelto = 1;
        $modpartidoapuesta->isganado    = 0;
        $modpartidoapuesta->modifyGanancia($conexion);

        $success_display = 'show';
        $success_text = 'Apuesta marcada como perdida.';

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_perdido
#region try
try {
    if(!empty($id_apuesta)){
        $partido_apuesta = PartidoApuesta::get($id_apuesta, $conexion);
    }
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lpartidos_apuestas_especifico.view.php';

?>