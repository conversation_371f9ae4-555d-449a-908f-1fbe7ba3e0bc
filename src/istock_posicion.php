<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/stockposicion.php';
require_once __ROOT__ . '/src/general/preparar.php';

$new_stock_posicion = new StockPosicion();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $new_stock_posicion->stock->ticker = limpiar_datos($_POST['ticker']);
        $new_stock_posicion->fecha         = limpiar_datos($_POST['fecha']);
        $new_stock_posicion->avg_price     = limpiar_datos($_POST['avg_price']);
        $new_stock_posicion->size          = limpiar_datos($_POST['size']);
        $new_stock_posicion->stoploss      = limpiar_datos($_POST['stoploss']);
        $new_stock_posicion->profit        = limpiar_datos($_POST['profit']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_agregar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar'])) {
    $in_transaction = false;
    
    try {
        $conexion->beginTransaction();
        $in_transaction = true;
        
        $new_stock_posicion->agregar($conexion);
    
        $conexion->commit();
        $in_transaction = false;

        header('Location: lstocks_posiciones?i=1');
        exit();
    
    } catch (Exception $e) {
        if($in_transaction == true){
            $conexion->rollback();
            $in_transaction = false;
        }
        
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_agregar
#region try
try {
    
    
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/istock_posicion.view.php';

?>