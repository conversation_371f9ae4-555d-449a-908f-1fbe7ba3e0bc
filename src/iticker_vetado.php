<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/tickervetado.php';
require_once __ROOT__ . '/src/classes/razoncomun.php';
require_once __ROOT__ . '/src/general/preparar.php';

$new_ticker_vetado = new TickerVetado();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		if (isset($_SESSION['ticker'])) {
			$ticker = $_SESSION['ticker'];
			
			$new_ticker_vetado->ticker = $ticker;
			
			unset($_SESSION['ticker']);
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion get
#region sub_agregar
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar'])) {
	try {
		$conexion->beginTransaction();
		
		$new_ticker_vetado->ticker         = limpiar_datos($_POST['ticker']);
		$new_ticker_vetado->fecha_revision = limpiar_datos($_POST['fecha_revision']);
		$new_ticker_vetado->razon          = limpiar_datos($_POST['razon']);
		$new_ticker_vetado->agregar($conexion);
		
		$conexion->commit();
		
		header('Location: ltickers_vetados?i=1');
		exit();
		
	} catch (Exception $e) {
		$conexion->rollback();
		
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_agregar
#region sub_agregar_razon_comun
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_razon_comun'])) {
	try {
		$new_razon_comun         = new RazonComun();
		$new_razon_comun->nombre = limpiar_datos($_POST['agregar_razon_comun_nombre']);
		$new_razon_comun->agregar($conexion);
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_agregar_razon_comun
#region try
try {
	$razones_comunes = RazonComun::get_list(array(), $conexion);

} catch (Exception $e) {
	$error_display = 'show';
	$error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/iticker_vetado.view.php';

?>




