<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/general/preparar.php';

$apuestas = array();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_search
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_search'])) {
    try {
        $method_sub_search = 1;
        
        $fchrange = limpiar_datos($_POST['fchrange']);

        $param = array();
        $param['fchrange'] = $fchrange;
        $param['solo_nota_analisis'] = 1;
        $apuestas = PartidoApuesta::get_list_byfecharange($param, $conexion);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_search
#region try
try {
    $method_try = 1;    

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/cpartidoreporte_analisis_apuestas.view.php';

?>





