<?php session_start();

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/scan.php';
require_once __ROOT__ . '/src/classes/sector.php';
require_once __ROOT__ . '/src/classes/pattern.php';
require_once __ROOT__ . '/src/general/preparar.php';

$sectortitle = "N/A";
$scans = array();

if ($_SERVER['REQUEST_METHOD'] == 'GET') { // new GET submit request $--start
    try {
        $date = Scan::get_lastdate($conexion);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
} 

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_search'])) {
    try {
        $ID_sector = limpiar_datos($_POST['ID_sector']);
        $date = limpiar_datos($_POST['date']);
        $ID_pattern = limpiar_datos($_POST['ID_pattern']);

        validar_textovacio($ID_sector, 'Specify sector');
        validar_textovacio($date, 'Specify date');

        $sectortitle = Sector::get_title($ID_sector, $conexion);
        $resultado = Scan::get_list($ID_sector,$date,"",$ID_pattern,$compare=1,$conexion);
        $scans = $resultado['scans'];

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_standby'])) {
    try {
        $ID_scan = limpiar_datos($_POST['standbyscan']);
        $standbyreason = limpiar_datos($_POST['standbyreason']);
        $ID_sector = limpiar_datos($_POST['standbysector']);
        $ID_pattern = limpiar_datos($_POST['standbypattern']);

        validar_textovacio($ID_scan, 'Specify scan ID');
        validar_textovacio($standbyreason, 'Specify reason');

        Scan::standby($ID_scan,$standbyreason,$conexion);

        $date = Scan::get_lastdate($conexion);
        
        if(isset($ID_sector)) {
            $sectortitle = Sector::get_title($ID_sector, $conexion);
            $resultado = Scan::get_list($ID_sector,$date,$ticker="",$ID_pattern,$compare=1,$conexion);
            $scans = $resultado['scans'];
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
} 

$sectors = Sector::get_list($conexion);
$patterns = Pattern::get_list($conexion);

require_once __ROOT__ . '/views/scancompare.view.php';

?>